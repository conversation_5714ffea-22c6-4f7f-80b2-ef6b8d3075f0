package search

import (
	"crypto/md5"
	"fmt"
	"net/url"
	"regexp"
	"strings"
	"time"

	"ultra_search/internal/models"

	"github.com/go-resty/resty/v2"
)

// GoogleProvider implements SearchProvider for Google Search
type GoogleProvider struct {
	client    *resty.Client
	baseURL   string
	rateLimit time.Duration
}

// NewGoogleProvider creates a new Google search provider
func NewGoogleProvider() *GoogleProvider {
	client := resty.New()
	client.SetTimeout(8 * time.Second) // Optimized timeout
	client.SetHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")

	return &GoogleProvider{
		client:    client,
		baseURL:   "https://www.google.com/search",
		rateLimit: 500 * time.Millisecond, // Much more aggressive for better performance
	}
}

// Name returns the provider name
func (g *GoogleProvider) Name() string {
	return "google"
}

// Search performs a search using Google web scraping
func (g *GoogleProvider) Search(request models.SearchRequest) ([]models.SearchResult, error) {
	// Build search URL
	searchURL := "https://www.google.com/search"
	params := url.Values{}
	params.Set("q", request.Query)
	params.Set("num", "20") // Request more results
	if request.Language != "" {
		params.Set("hl", request.Language)
	}
	if request.Region != "" {
		params.Set("gl", request.Region)
	}
	if request.SafeSearch {
		params.Set("safe", "active")
	}

	// Set headers to mimic a real browser
	resp, err := g.client.R().
		SetHeaders(map[string]string{
			"User-Agent":                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
			"Accept":                    "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
			"Accept-Language":           "en-US,en;q=0.5",
			"Accept-Encoding":           "gzip, deflate",
			"DNT":                       "1",
			"Connection":                "keep-alive",
			"Upgrade-Insecure-Requests": "1",
		}).
		SetQueryParamsFromValues(params).
		Get(searchURL)

	if err != nil {
		return nil, fmt.Errorf("google search failed: %w", err)
	}

	if resp.StatusCode() != 200 {
		return nil, fmt.Errorf("google returned status code: %d", resp.StatusCode())
	}

	return g.parseGoogleResults(resp.String(), request), nil
}

// parseGoogleResults parses Google search results from HTML
func (g *GoogleProvider) parseGoogleResults(html string, request models.SearchRequest) []models.SearchResult {
	var results []models.SearchResult

	// Regular expressions to extract search results
	// Google uses various div structures, we'll try to catch the most common ones
	resultRegex := regexp.MustCompile(`<div[^>]*data-ved="[^"]*"[^>]*>.*?<h3[^>]*>.*?<a[^>]*href="([^"]*)"[^>]*>(.*?)</a>.*?</h3>.*?<span[^>]*>(.*?)</span>`)

	// Alternative regex for different Google layouts
	altResultRegex := regexp.MustCompile(`<div[^>]*class="[^"]*g[^"]*"[^>]*>.*?<h3[^>]*>.*?<a[^>]*href="([^"]*)"[^>]*>(.*?)</a>.*?</h3>.*?<div[^>]*class="[^"]*VwiC3b[^"]*"[^>]*>(.*?)</div>`)

	// Try primary regex first
	matches := resultRegex.FindAllStringSubmatch(html, -1)
	if len(matches) == 0 {
		// Try alternative regex
		matches = altResultRegex.FindAllStringSubmatch(html, -1)
	}

	for i, match := range matches {
		if len(match) >= 4 && i < 20 { // Limit to 20 results
			rawURL := match[1]
			title := g.cleanHTML(match[2])
			description := g.cleanHTML(match[3])

			// Clean up Google's redirect URLs
			finalURL := g.cleanGoogleURL(rawURL)

			// Skip invalid URLs
			if finalURL == "" || strings.Contains(finalURL, "google.com") {
				continue
			}

			result := models.SearchResult{
				ID:          g.generateID(finalURL),
				Title:       title,
				URL:         finalURL,
				Description: description,
				Source:      g.Name(),
				Rank:        i + 1,
				Score:       0.95 - float64(i)*0.03, // Google results are typically high quality
				Timestamp:   time.Now(),
				Favicon:     "https://www.google.com/favicon.ico",
			}

			results = append(results, result)
		}
	}

	// If we didn't get results with regex, try a simpler approach
	if len(results) == 0 {
		results = g.parseGoogleResultsSimple(html, request)
	}

	return results
}

// parseGoogleResultsSimple is a fallback parser for Google results
func (g *GoogleProvider) parseGoogleResultsSimple(html string, request models.SearchRequest) []models.SearchResult {
	var results []models.SearchResult

	// Look for basic link patterns
	linkRegex := regexp.MustCompile(`<a[^>]*href="(/url\?q=|https?://)[^"]*"[^>]*>([^<]+)</a>`)
	matches := linkRegex.FindAllStringSubmatch(html, -1)

	for i, match := range matches {
		if i >= 10 { // Limit results
			break
		}

		title := g.cleanHTML(match[2])
		if len(title) < 10 || strings.Contains(title, "Google") {
			continue
		}

		result := models.SearchResult{
			ID:          g.generateID(fmt.Sprintf("google_simple_%d_%s", i, request.Query)),
			Title:       title,
			URL:         fmt.Sprintf("https://www.google.com/search?q=%s", url.QueryEscape(title)),
			Description: fmt.Sprintf("Search result for %s from Google", request.Query),
			Source:      g.Name(),
			Rank:        i + 1,
			Score:       0.8 - float64(i)*0.05,
			Timestamp:   time.Now(),
			Favicon:     "https://www.google.com/favicon.ico",
		}

		results = append(results, result)
	}

	return results
}

// cleanGoogleURL removes Google's redirect wrapper
func (g *GoogleProvider) cleanGoogleURL(rawURL string) string {
	// Handle /url?q= redirects
	if strings.HasPrefix(rawURL, "/url?q=") {
		parsed, err := url.Parse("https://www.google.com" + rawURL)
		if err != nil {
			return ""
		}
		return parsed.Query().Get("q")
	}

	// Handle direct URLs
	if strings.HasPrefix(rawURL, "http") {
		return rawURL
	}

	return ""
}

// cleanHTML removes HTML tags and decodes entities
func (g *GoogleProvider) cleanHTML(text string) string {
	// Remove HTML tags
	tagRegex := regexp.MustCompile(`<[^>]*>`)
	text = tagRegex.ReplaceAllString(text, "")

	// Decode common HTML entities
	text = strings.ReplaceAll(text, "&amp;", "&")
	text = strings.ReplaceAll(text, "&lt;", "<")
	text = strings.ReplaceAll(text, "&gt;", ">")
	text = strings.ReplaceAll(text, "&quot;", "\"")
	text = strings.ReplaceAll(text, "&#39;", "'")
	text = strings.ReplaceAll(text, "&nbsp;", " ")

	// Clean up whitespace
	text = regexp.MustCompile(`\s+`).ReplaceAllString(text, " ")
	text = strings.TrimSpace(text)

	return text
}

// generateMockResults generates mock results for demonstration
// In production, replace this with actual Google API integration
func (g *GoogleProvider) generateMockResults(request models.SearchRequest) []models.SearchResult {
	var results []models.SearchResult

	// Generate some mock results based on the query
	mockData := []struct {
		title       string
		urlPattern  string
		description string
	}{
		{
			title:       fmt.Sprintf("%s - Google Search Results", strings.Title(request.Query)),
			urlPattern:  "https://www.google.com/search?q=%s",
			description: fmt.Sprintf("Search results for %s from Google. This is a comprehensive overview of the topic.", request.Query),
		},
		{
			title:       fmt.Sprintf("What is %s? - Comprehensive Guide", request.Query),
			urlPattern:  "https://example.com/guide/%s",
			description: fmt.Sprintf("A detailed guide explaining %s, its uses, benefits, and everything you need to know.", request.Query),
		},
		{
			title:       fmt.Sprintf("%s: Latest News and Updates", strings.Title(request.Query)),
			urlPattern:  "https://news.example.com/%s",
			description: fmt.Sprintf("Stay updated with the latest news and developments related to %s.", request.Query),
		},
		{
			title:       fmt.Sprintf("How to use %s effectively", request.Query),
			urlPattern:  "https://tutorial.example.com/%s",
			description: fmt.Sprintf("Learn how to effectively use %s with step-by-step instructions and best practices.", request.Query),
		},
		{
			title:       fmt.Sprintf("%s Reviews and Comparisons", strings.Title(request.Query)),
			urlPattern:  "https://reviews.example.com/%s",
			description: fmt.Sprintf("Read honest reviews and detailed comparisons of %s to make informed decisions.", request.Query),
		},
		{
			title:       fmt.Sprintf("Free %s Resources and Tools", request.Query),
			urlPattern:  "https://resources.example.com/%s",
			description: fmt.Sprintf("Discover free resources, tools, and materials related to %s.", request.Query),
		},
	}

	for i, data := range mockData {
		if i >= 6 { // Limit to 6 mock results
			break
		}

		url := fmt.Sprintf(data.urlPattern, strings.ReplaceAll(request.Query, " ", "-"))

		result := models.SearchResult{
			ID:          g.generateID(fmt.Sprintf("google_%s_%d", request.Query, i)),
			Title:       data.title,
			URL:         url,
			Description: data.description,
			Source:      g.Name(),
			Rank:        i + 1,
			Score:       0.95 - float64(i)*0.08, // Google typically has high-quality results
			Timestamp:   time.Now(),
			Favicon:     "https://www.google.com/favicon.ico",
		}

		results = append(results, result)
	}

	return results
}

// generateID generates a unique ID for a result
func (g *GoogleProvider) generateID(content string) string {
	hash := md5.Sum([]byte(content))
	return fmt.Sprintf("google_%x", hash)[:16]
}

// IsAvailable checks if the provider is available
func (g *GoogleProvider) IsAvailable() bool {
	// For mock implementation, always return true
	// In production, you would check the actual Google API endpoint
	return true
}

// GetRateLimit returns the rate limit for this provider
func (g *GoogleProvider) GetRateLimit() time.Duration {
	return g.rateLimit
}
