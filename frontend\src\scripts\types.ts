// Type definitions for Ultra Search
export interface SearchResult {
  title: string;
  url: string;
  description: string;
  source: string;
  score: number;
  rank: number;
}

export interface SearchResponse {
  results: SearchResult[];
  total_count: number;
  page: number;
  per_page: number;
  search_time: number;
  sources: string[];
  provider_stats: ProviderStatistic[];
  processing_time: ProcessingTimes;
}

export interface ProviderStatistic {
  name: string;
  duration: number;
  result_count: number;
  success: boolean;
  error?: string;
  rate_limit: number;
  response_time: number;
}

export interface ProcessingTimes {
  search_time: number;
  deduplication_time: number;
  ranking_time: number;
  enhancement_time: number;
}

export interface AutocompleteResponse {
  suggestions: string[];
}

export interface SearchState {
  currentQuery: string;
  currentPage: number;
  isLoading: boolean;
}
