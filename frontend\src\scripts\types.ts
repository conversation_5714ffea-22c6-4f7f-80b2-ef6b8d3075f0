// Type definitions for Ultra Search
export interface SearchResult {
  title: string;
  url: string;
  description: string;
  source: string;
  score: number;
  rank: number;
}

export interface SearchResponse {
  results: SearchResult[];
  total_count: number;
  page: number;
  per_page: number;
  search_time: number;
  sources: string[];
}

export interface AutocompleteResponse {
  suggestions: string[];
}

export interface SearchState {
  currentQuery: string;
  currentPage: number;
  isLoading: boolean;
}
