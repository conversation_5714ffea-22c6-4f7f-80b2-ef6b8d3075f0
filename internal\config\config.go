package config

import (
	"os"
	"strconv"
	"time"
)

// Config holds all configuration for the application
type Config struct {
	Server    ServerConfig
	Search    SearchConfig
	Cache     CacheConfig
	RateLimit RateLimitConfig
	Logging   LoggingConfig
}

// ServerConfig holds server-related configuration
type ServerConfig struct {
	Port         string
	Host         string
	ReadTimeout  time.Duration
	WriteTimeout time.Duration
	IdleTimeout  time.Duration
	CORS         CORSConfig
}

// CORSConfig holds CORS configuration
type CORSConfig struct {
	AllowedOrigins []string
	AllowedMethods []string
	AllowedHeaders []string
}

// SearchConfig holds search-related configuration
type SearchConfig struct {
	MaxConcurrentProviders int
	SearchTimeout          time.Duration
	MaxResultsPerProvider  int
	EnabledProviders       []string
	DefaultResultsPerPage  int
	MaxResultsPerPage      int
}

// CacheConfig holds cache-related configuration
type CacheConfig struct {
	TTL             time.Duration
	CleanupInterval time.Duration
	MaxSize         int
}

// RateLimitConfig holds rate limiting configuration
type RateLimitConfig struct {
	RequestsPerMinute int
	BurstSize         int
}

// LoggingConfig holds logging configuration
type LoggingConfig struct {
	Level  string
	Format string
}

// Load loads configuration from environment variables with defaults
func Load() *Config {
	return &Config{
		Server: ServerConfig{
			Port:         getEnv("PORT", "8080"),
			Host:         getEnv("HOST", "0.0.0.0"),
			ReadTimeout:  getDurationEnv("READ_TIMEOUT", 10*time.Second),
			WriteTimeout: getDurationEnv("WRITE_TIMEOUT", 10*time.Second),
			IdleTimeout:  getDurationEnv("IDLE_TIMEOUT", 60*time.Second),
			CORS: CORSConfig{
				AllowedOrigins: []string{"*"},
				AllowedMethods: []string{"GET", "POST", "OPTIONS"},
				AllowedHeaders: []string{"Content-Type", "Authorization"},
			},
		},
		Search: SearchConfig{
			MaxConcurrentProviders: getIntEnv("MAX_CONCURRENT_PROVIDERS", 5),
			SearchTimeout:          getDurationEnv("SEARCH_TIMEOUT", 8*time.Second),
			MaxResultsPerProvider:  getIntEnv("MAX_RESULTS_PER_PROVIDER", 20),
			EnabledProviders:       []string{"duckduckgo", "bing", "google", "yahoo", "startpage", "brave"},
			DefaultResultsPerPage:  getIntEnv("DEFAULT_RESULTS_PER_PAGE", 10),
			MaxResultsPerPage:      getIntEnv("MAX_RESULTS_PER_PAGE", 50),
		},
		Cache: CacheConfig{
			TTL:             getDurationEnv("CACHE_TTL", 5*time.Minute),
			CleanupInterval: getDurationEnv("CACHE_CLEANUP_INTERVAL", 10*time.Minute),
			MaxSize:         getIntEnv("CACHE_MAX_SIZE", 1000),
		},
		RateLimit: RateLimitConfig{
			RequestsPerMinute: getIntEnv("RATE_LIMIT_RPM", 60),
			BurstSize:         getIntEnv("RATE_LIMIT_BURST", 10),
		},
		Logging: LoggingConfig{
			Level:  getEnv("LOG_LEVEL", "info"),
			Format: getEnv("LOG_FORMAT", "json"),
		},
	}
}

// Helper functions for environment variable parsing
func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

func getIntEnv(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.Atoi(value); err == nil {
			return intValue
		}
	}
	return defaultValue
}

func getDurationEnv(key string, defaultValue time.Duration) time.Duration {
	if value := os.Getenv(key); value != "" {
		if duration, err := time.ParseDuration(value); err == nil {
			return duration
		}
	}
	return defaultValue
}
