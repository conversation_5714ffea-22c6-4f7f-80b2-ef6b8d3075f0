---
// Results page - Google-style streamlined layout
import Layout from '../layouts/Layout.astro';
import "../styles/global.css";
---

<Layout title="Ultra Search Results">
  <!-- Top search bar -->
  <div class="border-b border-gray-200 dark:border-gray-700 pb-4 mb-6">
    <div class="max-w-2xl mx-auto px-4 py-4 flex items-center gap-4">
      <h1 class="text-2xl font-normal text-blue-600 dark:text-blue-400">Ultra Search</h1>
      <form class="flex-1">
        <input
          type="text"
          value="javascript frameworks"
          class="w-full px-4 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-full focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
        />
      </form>
    </div>
  </div>

  <div class="max-w-2xl mx-auto px-4">
    <!-- Search info -->
    <div class="text-sm text-gray-600 dark:text-gray-400 mb-4">
      About 1,234,567 results (0.45 seconds)
    </div>

    <!-- Results -->
    <div class="space-y-6">
      <!-- Result 1 -->
      <div>
        <div class="text-sm text-green-700 dark:text-green-400 mb-1">reactjs.org</div>
        <h3 class="text-xl text-blue-600 dark:text-blue-400 hover:underline mb-1">
          <a href="#" target="_blank">React – A JavaScript library for building user interfaces</a>
        </h3>
        <p class="text-gray-600 dark:text-gray-400 text-sm leading-relaxed">
          React makes it painless to create interactive UIs. Design simple views for each state in your application, and React will efficiently update and render just the right components when your data changes.
        </p>
        <div class="text-xs text-gray-500 mt-1">
          google • Score: 0.95
        </div>
      </div>

      <!-- Result 2 -->
      <div>
        <div class="text-sm text-green-700 dark:text-green-400 mb-1">vuejs.org</div>
        <h3 class="text-xl text-blue-600 dark:text-blue-400 hover:underline mb-1">
          <a href="#" target="_blank">Vue.js - The Progressive JavaScript Framework</a>
        </h3>
        <p class="text-gray-600 dark:text-gray-400 text-sm leading-relaxed">
          Vue.js is a progressive, incrementally-adoptable JavaScript framework for building UI on the web. It is designed from the ground up to be incrementally adoptable.
        </p>
        <div class="text-xs text-gray-500 mt-1">
          bing • Score: 0.92
        </div>
      </div>

      <!-- Result 3 -->
      <div>
        <div class="text-sm text-green-700 dark:text-green-400 mb-1">angular.io</div>
        <h3 class="text-xl text-blue-600 dark:text-blue-400 hover:underline mb-1">
          <a href="#" target="_blank">Angular - The modern web developer's platform</a>
        </h3>
        <p class="text-gray-600 dark:text-gray-400 text-sm leading-relaxed">
          Angular is a platform for building mobile and desktop web applications. Join the community of millions of developers who build compelling user interfaces with Angular.
        </p>
        <div class="text-xs text-gray-500 mt-1">
          duckduckgo • Score: 0.89
        </div>
      </div>

      <!-- Result 4 -->
      <div>
        <div class="text-sm text-green-700 dark:text-green-400 mb-1">svelte.dev</div>
        <h3 class="text-xl text-blue-600 dark:text-blue-400 hover:underline mb-1">
          <a href="#" target="_blank">Svelte • Cybernetically enhanced web apps</a>
        </h3>
        <p class="text-gray-600 dark:text-gray-400 text-sm leading-relaxed">
          Svelte is a radical new approach to building user interfaces. Whereas traditional frameworks like React and Vue do the bulk of their work in the browser, Svelte shifts that work into a compile step.
        </p>
        <div class="text-xs text-gray-500 mt-1">
          yahoo • Score: 0.87
        </div>
      </div>

      <!-- Result 5 -->
      <div>
        <div class="text-sm text-green-700 dark:text-green-400 mb-1">github.com</div>
        <h3 class="text-xl text-blue-600 dark:text-blue-400 hover:underline mb-1">
          <a href="#" target="_blank">Top JavaScript Frameworks 2024 - GitHub</a>
        </h3>
        <p class="text-gray-600 dark:text-gray-400 text-sm leading-relaxed">
          A comprehensive list of the most popular JavaScript frameworks in 2024, including React, Vue, Angular, Svelte, and more. Compare features, performance, and community support.
        </p>
        <div class="text-xs text-gray-500 mt-1">
          startpage • Score: 0.84
        </div>
      </div>

      <!-- Result 6 -->
      <div>
        <div class="text-sm text-green-700 dark:text-green-400 mb-1">developer.mozilla.org</div>
        <h3 class="text-xl text-blue-600 dark:text-blue-400 hover:underline mb-1">
          <a href="#" target="_blank">JavaScript frameworks and libraries - MDN Web Docs</a>
        </h3>
        <p class="text-gray-600 dark:text-gray-400 text-sm leading-relaxed">
          Learn about popular JavaScript frameworks and libraries. This guide covers React, Vue, Angular, and other modern tools for building web applications.
        </p>
        <div class="text-xs text-gray-500 mt-1">
          brave • Score: 0.82
        </div>
      </div>
    </div>

    <!-- Pagination -->
    <div class="flex justify-center gap-2 mt-8 mb-16">
      <button class="px-3 py-2 text-blue-600 hover:underline">Previous</button>
      <button class="px-3 py-2 font-bold text-black">1</button>
      <button class="px-3 py-2 text-blue-600 hover:underline">2</button>
      <button class="px-3 py-2 text-blue-600 hover:underline">3</button>
      <button class="px-3 py-2 text-blue-600 hover:underline">4</button>
      <button class="px-3 py-2 text-blue-600 hover:underline">5</button>
      <button class="px-3 py-2 text-blue-600 hover:underline">Next</button>
    </div>
  </div>
</Layout>
