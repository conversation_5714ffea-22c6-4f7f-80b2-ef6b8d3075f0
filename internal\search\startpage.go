package search

import (
	"crypto/md5"
	"fmt"
	"net/url"
	"regexp"
	"strings"
	"time"

	"ultra_search/internal/models"

	"github.com/go-resty/resty/v2"
)

// StartpageProvider implements SearchProvider for Startpage Search
type StartpageProvider struct {
	client    *resty.Client
	baseURL   string
	rateLimit time.Duration
}

// NewStartpageProvider creates a new Startpage search provider
func NewStartpageProvider() *StartpageProvider {
	client := resty.New()
	client.SetTimeout(12 * time.Second)
	client.SetHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
	client.SetHeader("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8")
	client.SetHeader("Accept-Language", "en-US,en;q=0.9")
	client.SetHeader("Accept-Encoding", "gzip, deflate, br")
	client.SetHeader("DNT", "1")
	client.SetHeader("Connection", "keep-alive")
	client.SetHeader("Upgrade-Insecure-Requests", "1")
	client.SetHeader("Sec-Fetch-Dest", "document")
	client.SetHeader("Sec-Fetch-Mode", "navigate")
	client.SetHeader("Sec-Fetch-Site", "none")

	return &StartpageProvider{
		client:    client,
		baseURL:   "https://www.startpage.com/sp/search",
		rateLimit: 2 * time.Second, // Conservative rate limit for privacy-focused engine
	}
}

// Name returns the provider name
func (s *StartpageProvider) Name() string {
	return "startpage"
}

// IsAvailable checks if the provider is available
func (s *StartpageProvider) IsAvailable() bool {
	return true
}

// GetRateLimit returns the rate limit for this provider
func (s *StartpageProvider) GetRateLimit() time.Duration {
	return s.rateLimit
}

// Search performs a search using Startpage web scraping
func (s *StartpageProvider) Search(request models.SearchRequest) ([]models.SearchResult, error) {
	// Build search URL
	params := url.Values{}
	params.Set("query", request.Query)
	params.Set("cat", "web")
	params.Set("pl", "opensearch")
	params.Set("language", "english")
	if request.Language != "" {
		params.Set("language", request.Language)
	}
	if request.SafeSearch {
		params.Set("abp", "1") // Enable safe search
	}

	searchURL := s.baseURL + "?" + params.Encode()

	// Make the request
	resp, err := s.client.R().Get(searchURL)
	if err != nil {
		return nil, fmt.Errorf("startpage search request failed: %w", err)
	}

	if resp.StatusCode() != 200 {
		return nil, fmt.Errorf("startpage search returned status %d", resp.StatusCode())
	}

	// Parse results
	results := s.parseStartpageResults(string(resp.Body()), request)
	return results, nil
}

// parseStartpageResults parses Startpage search results from HTML
func (s *StartpageProvider) parseStartpageResults(html string, request models.SearchRequest) []models.SearchResult {
	var results []models.SearchResult

	// Startpage uses specific class names for search results
	// Primary regex for Startpage organic results
	resultRegex := regexp.MustCompile(`<div[^>]*class="[^"]*w-gl__result[^"]*"[^>]*>.*?<h3[^>]*class="[^"]*w-gl__result-title[^"]*"[^>]*>.*?<a[^>]*href="([^"]*)"[^>]*>(.*?)</a>.*?</h3>.*?<p[^>]*class="[^"]*w-gl__description[^"]*"[^>]*>(.*?)</p>`)

	// Alternative regex for different Startpage layouts
	altResultRegex := regexp.MustCompile(`<div[^>]*class="[^"]*result[^"]*"[^>]*>.*?<h3[^>]*>.*?<a[^>]*href="([^"]*)"[^>]*>(.*?)</a>.*?</h3>.*?<p[^>]*>(.*?)</p>`)

	// Simplified regex for basic results
	simpleRegex := regexp.MustCompile(`<a[^>]*class="[^"]*result-link[^"]*"[^>]*href="([^"]*)"[^>]*>(.*?)</a>`)

	// Try primary regex first
	matches := resultRegex.FindAllStringSubmatch(html, -1)
	if len(matches) == 0 {
		// Try alternative regex
		matches = altResultRegex.FindAllStringSubmatch(html, -1)
	}

	for i, match := range matches {
		if len(match) >= 4 && i < 20 { // Limit to 20 results
			rawURL := match[1]
			title := s.cleanHTML(match[2])
			description := s.cleanHTML(match[3])

			// Clean up Startpage's proxy URLs
			finalURL := s.cleanStartpageURL(rawURL)

			// Skip invalid URLs
			if finalURL == "" || strings.Contains(finalURL, "startpage.com") {
				continue
			}

			result := models.SearchResult{
				ID:          s.generateID(finalURL),
				Title:       title,
				URL:         finalURL,
				Description: description,
				Source:      s.Name(),
				Rank:        i + 1,
				Score:       0.88 - float64(i)*0.03, // Startpage provides Google results with privacy
				Timestamp:   time.Now(),
				Favicon:     "https://www.startpage.com/favicon.ico",
			}

			results = append(results, result)
		}
	}

	// If we didn't get results with complex regex, try simple approach
	if len(results) == 0 {
		matches = simpleRegex.FindAllStringSubmatch(html, -1)
		for i, match := range matches {
			if len(match) >= 3 && i < 15 {
				rawURL := match[1]
				title := s.cleanHTML(match[2])

				finalURL := s.cleanStartpageURL(rawURL)
				if finalURL == "" || strings.Contains(finalURL, "startpage.com") {
					continue
				}

				result := models.SearchResult{
					ID:          s.generateID(finalURL),
					Title:       title,
					URL:         finalURL,
					Description: fmt.Sprintf("Search result for %s from Startpage", request.Query),
					Source:      s.Name(),
					Rank:        i + 1,
					Score:       0.80 - float64(i)*0.04,
					Timestamp:   time.Now(),
					Favicon:     "https://www.startpage.com/favicon.ico",
				}

				results = append(results, result)
			}
		}
	}

	return results
}

// cleanStartpageURL cleans Startpage's proxy URLs
func (s *StartpageProvider) cleanStartpageURL(rawURL string) string {
	// Startpage uses proxy URLs like /sp/search?query=...&url=encoded_url
	if strings.Contains(rawURL, "startpage.com") && strings.Contains(rawURL, "url=") {
		if u, err := url.Parse(rawURL); err == nil {
			if targetURL := u.Query().Get("url"); targetURL != "" {
				if decoded, err := url.QueryUnescape(targetURL); err == nil {
					return decoded
				}
			}
		}
	}

	// Handle relative URLs
	if strings.HasPrefix(rawURL, "/") && !strings.HasPrefix(rawURL, "//") {
		return "https://www.startpage.com" + rawURL
	}

	return rawURL
}

// cleanHTML removes HTML tags and decodes entities
func (s *StartpageProvider) cleanHTML(text string) string {
	// Remove HTML tags
	re := regexp.MustCompile(`<[^>]*>`)
	text = re.ReplaceAllString(text, "")

	// Decode common HTML entities
	text = strings.ReplaceAll(text, "&amp;", "&")
	text = strings.ReplaceAll(text, "&lt;", "<")
	text = strings.ReplaceAll(text, "&gt;", ">")
	text = strings.ReplaceAll(text, "&quot;", "\"")
	text = strings.ReplaceAll(text, "&#39;", "'")
	text = strings.ReplaceAll(text, "&nbsp;", " ")
	text = strings.ReplaceAll(text, "&#x27;", "'")
	text = strings.ReplaceAll(text, "&#x2F;", "/")

	// Clean up whitespace
	text = regexp.MustCompile(`\s+`).ReplaceAllString(text, " ")
	return strings.TrimSpace(text)
}

// generateID generates a unique ID for a search result
func (s *StartpageProvider) generateID(url string) string {
	hash := md5.Sum([]byte(url))
	return fmt.Sprintf("startpage_%x", hash)
}
