// UI utilities - rendering and state management
import type { SearchResponse, SearchResult } from './types.ts';
import { elements } from './dom.ts';

export class UIManager {
  static showLoading(): void {
    elements.loading?.classList.remove('hidden');
    if (elements.results) elements.results.innerHTML = '';
    elements.searchInfo?.classList.add('hidden');
    elements.pagination?.classList.add('hidden');

    // Start enhanced loading animation
    if ((window as any).loadingAnimation) {
      (window as any).loadingAnimation.start();
    }
  }

  static hideLoading(): void {
    elements.loading?.classList.add('hidden');

    // Stop enhanced loading animation
    if ((window as any).loadingAnimation) {
      (window as any).loadingAnimation.stop();
    }
  }

  static displayError(message: string): void {
    if (elements.results) {
      elements.results.innerHTML = `
        <div class="text-center py-16">
          <div class="w-16 h-16 mx-auto mb-4 bg-red-100 rounded-full flex items-center justify-center">
            <svg class="w-8 h-8 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
          </div>
          <p class="text-red-500 text-lg font-medium">${message}</p>
          <p class="text-slate-400 text-sm mt-1">Please try again</p>
        </div>
      `;
    }
    elements.searchInfo?.classList.add('hidden');
    elements.pagination?.classList.add('hidden');
  }

  static displayResults(data: SearchResponse): void {
    if (!elements.results) return;

    if (!data.results || data.results.length === 0) {
      elements.results.innerHTML = `
        <div class="text-center py-8">
          <p class="text-gray-600 dark:text-gray-400">No results found</p>
        </div>
      `;
      return;
    }

    elements.results.innerHTML = data.results.map((result: SearchResult, index: number) => {
      const hostname = new URL(result.url).hostname;
      const favicon = result.favicon || `https://www.google.com/s2/favicons?domain=${hostname}&sz=16`;

      return `
        <article class="card p-6 mb-6 hover-lift fade-in" style="animation-delay: ${index * 0.1}s">
          <!-- Result header -->
          <div class="flex items-center gap-2 mb-2">
            <img src="${favicon}" alt="${hostname}" class="w-4 h-4 rounded-sm" onerror="this.style.display='none'">
            <div class="text-sm font-medium text-green-700 dark:text-green-400">${hostname}</div>
            <div class="flex items-center gap-1 ml-auto">
              <div class="w-2 h-2 rounded-full bg-${UIManager.getProviderColor(result.source)}"></div>
              <span class="text-xs text-gray-500 dark:text-gray-400">${result.source}</span>
            </div>
          </div>

          <!-- Result title -->
          <h3 class="text-xl font-semibold mb-2">
            <a href="${result.url}"
               target="_blank"
               rel="noopener noreferrer"
               class="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 transition-colors duration-200 line-clamp-2">
              ${result.title}
            </a>
          </h3>

          <!-- Result description -->
          <p class="text-gray-700 dark:text-gray-300 text-sm leading-relaxed mb-3 line-clamp-3">
            ${result.description}
          </p>

          <!-- Result metadata -->
          <div class="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
            <div class="flex items-center gap-3">
              <span class="flex items-center gap-1">
                <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                </svg>
                Score: ${result.score.toFixed(2)}
              </span>
              <span class="flex items-center gap-1">
                <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"></path>
                </svg>
                Rank: #${result.rank}
              </span>
            </div>

            <!-- Quick actions -->
            <div class="flex items-center gap-2">
              <button class="p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded transition-colors duration-200"
                      onclick="UIManager.shareResult('${result.title.replace(/'/g, "\\'")}', '${result.url}')"
                      title="Share">
                <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"></path>
                </svg>
              </button>
              <button class="p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded transition-colors duration-200"
                      onclick="UIManager.copyToClipboard('${result.url}')"
                      title="Copy link">
                <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                </svg>
              </button>
            </div>
          </div>
        </article>
      `;
    }).join('');
  }

  static updateSearchInfo(data: SearchResponse): void {
    if (!elements.searchInfo) return;

    const searchTime = (data.search_time / 1000000).toFixed(0);
    const providerStats = data.provider_stats || [];
    const processingTime = data.processing_time;

    // Calculate total successful providers and results
    const successfulProviders = providerStats.filter(p => p.success);
    const totalProviderResults = providerStats.reduce((sum, p) => sum + p.result_count, 0);

    // Build provider breakdown
    const providerBreakdown = successfulProviders
      .map(p => {
        const responseTime = (p.response_time / 1000000).toFixed(0);
        const resultText = p.result_count === 1 ? 'result' : 'results';
        return `${p.name}: ${p.result_count} ${resultText} (${responseTime}ms)`;
      })
      .join(' • ');

    // Build detailed timing breakdown
    let timingDetails = '';
    if (processingTime) {
      const searchMs = (processingTime.search_time / 1000000).toFixed(0);
      const dedupeMs = (processingTime.deduplication_time / 1000000).toFixed(0);
      const rankMs = (processingTime.ranking_time / 1000000).toFixed(0);
      const enhanceMs = (processingTime.enhancement_time / 1000000).toFixed(0);

      timingDetails = `
        <div class="text-xs text-gray-500 mt-1 space-y-1">
          <div class="flex flex-wrap gap-3">
            <span>Search: ${searchMs}ms</span>
            <span>Dedup: ${dedupeMs}ms</span>
            <span>Rank: ${rankMs}ms</span>
            <span>Enhance: ${enhanceMs}ms</span>
          </div>
          <div class="text-gray-400">${providerBreakdown}</div>
        </div>
      `;
    }

    elements.searchInfo.innerHTML = `
      <div class="flex items-center justify-between">
        <div>
          About ${data.total_count.toLocaleString()} results from ${successfulProviders.length} providers (${searchTime}ms)
        </div>
        <button id="toggle-stats" class="text-xs text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300">
          Stats
        </button>
      </div>
      <div id="detailed-stats" class="hidden">
        ${timingDetails}
      </div>
    `;

    // Add toggle functionality for detailed stats
    const toggleButton = elements.searchInfo.querySelector('#toggle-stats');
    const detailedStats = elements.searchInfo.querySelector('#detailed-stats');

    if (toggleButton && detailedStats) {
      toggleButton.addEventListener('click', () => {
        const isHidden = detailedStats.classList.contains('hidden');
        if (isHidden) {
          detailedStats.classList.remove('hidden');
          toggleButton.textContent = 'Hide';
        } else {
          detailedStats.classList.add('hidden');
          toggleButton.textContent = 'Stats';
        }
      });
    }

    elements.searchInfo.classList.remove('hidden');
  }

  static updatePagination(data: SearchResponse, onPageClick: (page: number) => void): void {
    if (!elements.pagination) return;

    const totalPages = Math.ceil(data.total_count / data.per_page);

    if (totalPages <= 1) {
      elements.pagination.classList.add('hidden');
      return;
    }

    let paginationHTML = '';

    // Previous
    if (data.page > 1) {
      paginationHTML += `<button data-page="${data.page - 1}" class="pagination-btn px-3 py-2 text-blue-600 hover:underline">Previous</button>`;
    }

    // Page numbers
    const startPage = Math.max(1, data.page - 2);
    const endPage = Math.min(totalPages, data.page + 2);

    for (let i = startPage; i <= endPage; i++) {
      const isActive = i === data.page;
      paginationHTML += `<button data-page="${i}" class="pagination-btn px-3 py-2 ${isActive ? 'font-bold text-black' : 'text-blue-600 hover:underline'}">${i}</button>`;
    }

    // Next
    if (data.page < totalPages) {
      paginationHTML += `<button data-page="${data.page + 1}" class="pagination-btn px-3 py-2 text-blue-600 hover:underline">Next</button>`;
    }

    elements.pagination.innerHTML = paginationHTML;

    elements.pagination.querySelectorAll('.pagination-btn').forEach(button => {
      button.addEventListener('click', (e) => {
        const page = parseInt((e.target as HTMLButtonElement).dataset.page || '1');
        onPageClick(page);
      });
    });

    elements.pagination.classList.remove('hidden');
  }

  static getProviderColor(provider: string): string {
    const colors: { [key: string]: string } = {
      'google': 'blue-500',
      'bing': 'orange-500',
      'duckduckgo': 'red-500',
      'yahoo': 'purple-500',
      'startpage': 'green-500',
      'brave': 'orange-600'
    };
    return colors[provider.toLowerCase()] || 'gray-500';
  }

  static async shareResult(title: string, url: string): Promise<void> {
    if (navigator.share) {
      try {
        await navigator.share({ title, url });
      } catch (error) {
        // Fallback to clipboard
        this.copyToClipboard(url);
      }
    } else {
      this.copyToClipboard(url);
    }
  }

  static async copyToClipboard(text: string): Promise<void> {
    try {
      await navigator.clipboard.writeText(text);
      this.showToast('Link copied to clipboard!');
    } catch (error) {
      // Fallback for older browsers
      const textArea = document.createElement('textarea');
      textArea.value = text;
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);
      this.showToast('Link copied to clipboard!');
    }
  }

  static showToast(message: string): void {
    // Create toast element
    const toast = document.createElement('div');
    toast.className = 'fixed bottom-20 right-6 bg-gray-900 dark:bg-gray-100 text-white dark:text-gray-900 px-4 py-2 rounded-lg shadow-lg z-50 transition-all duration-300 transform translate-y-full opacity-0';
    toast.textContent = message;

    document.body.appendChild(toast);

    // Animate in
    setTimeout(() => {
      toast.classList.remove('translate-y-full', 'opacity-0');
    }, 100);

    // Animate out and remove
    setTimeout(() => {
      toast.classList.add('translate-y-full', 'opacity-0');
      setTimeout(() => {
        document.body.removeChild(toast);
      }, 300);
    }, 3000);
  }
}
