// UI utilities - rendering and state management
import type { SearchResponse, SearchResult } from './types.ts';
import { elements } from './dom.ts';

export class UIManager {
  static showLoading(): void {
    elements.loading?.classList.remove('hidden');
    if (elements.results) elements.results.innerHTML = '';
    elements.searchInfo?.classList.add('hidden');
    elements.pagination?.classList.add('hidden');
  }

  static hideLoading(): void {
    elements.loading?.classList.add('hidden');
  }

  static displayError(message: string): void {
    if (elements.results) {
      elements.results.innerHTML = `
        <div class="text-center py-16">
          <div class="w-16 h-16 mx-auto mb-4 bg-red-100 rounded-full flex items-center justify-center">
            <svg class="w-8 h-8 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
          </div>
          <p class="text-red-500 text-lg font-medium">${message}</p>
          <p class="text-slate-400 text-sm mt-1">Please try again</p>
        </div>
      `;
    }
    elements.searchInfo?.classList.add('hidden');
    elements.pagination?.classList.add('hidden');
  }

  static displayResults(data: SearchResponse): void {
    if (!elements.results) return;

    if (!data.results || data.results.length === 0) {
      elements.results.innerHTML = `
        <div class="text-center py-8">
          <p class="text-gray-600 dark:text-gray-400">No results found</p>
        </div>
      `;
      return;
    }

    elements.results.innerHTML = data.results.map((result: SearchResult) => {
      return `
        <div class="mb-6">
          <div class="text-sm text-green-700 dark:text-green-400 mb-1">${new URL(result.url).hostname}</div>
          <h3 class="text-xl text-blue-600 dark:text-blue-400 hover:underline mb-1">
            <a href="${result.url}" target="_blank" rel="noopener noreferrer">
              ${result.title}
            </a>
          </h3>
          <p class="text-gray-600 dark:text-gray-400 text-sm leading-relaxed">${result.description}</p>
          <div class="text-xs text-gray-500 mt-1">
            ${result.source} • Score: ${result.score.toFixed(2)}
          </div>
        </div>
      `;
    }).join('');
  }

  static updateSearchInfo(data: SearchResponse): void {
    if (!elements.searchInfo) return;

    const searchTime = (data.search_time / 1000000).toFixed(0);

    elements.searchInfo.innerHTML = `
      About ${data.total_count.toLocaleString()} results (${searchTime}ms)
    `;
    elements.searchInfo.classList.remove('hidden');
  }

  static updatePagination(data: SearchResponse, onPageClick: (page: number) => void): void {
    if (!elements.pagination) return;

    const totalPages = Math.ceil(data.total_count / data.per_page);

    if (totalPages <= 1) {
      elements.pagination.classList.add('hidden');
      return;
    }

    let paginationHTML = '';

    // Previous
    if (data.page > 1) {
      paginationHTML += `<button data-page="${data.page - 1}" class="pagination-btn px-3 py-2 text-blue-600 hover:underline">Previous</button>`;
    }

    // Page numbers
    const startPage = Math.max(1, data.page - 2);
    const endPage = Math.min(totalPages, data.page + 2);

    for (let i = startPage; i <= endPage; i++) {
      const isActive = i === data.page;
      paginationHTML += `<button data-page="${i}" class="pagination-btn px-3 py-2 ${isActive ? 'font-bold text-black' : 'text-blue-600 hover:underline'}">${i}</button>`;
    }

    // Next
    if (data.page < totalPages) {
      paginationHTML += `<button data-page="${data.page + 1}" class="pagination-btn px-3 py-2 text-blue-600 hover:underline">Next</button>`;
    }

    elements.pagination.innerHTML = paginationHTML;

    elements.pagination.querySelectorAll('.pagination-btn').forEach(button => {
      button.addEventListener('click', (e) => {
        const page = parseInt((e.target as HTMLButtonElement).dataset.page || '1');
        onPageClick(page);
      });
    });

    elements.pagination.classList.remove('hidden');
  }
}
