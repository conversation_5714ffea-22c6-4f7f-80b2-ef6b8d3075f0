package search

import (
	"crypto/md5"
	"encoding/json"
	"fmt"
	"net/url"
	"strings"
	"time"

	"ultra_search/internal/models"

	"github.com/go-resty/resty/v2"
)

// DuckDuckGoProvider implements SearchProvider for DuckDuckGo
type DuckDuckGoProvider struct {
	client    *resty.Client
	baseURL   string
	rateLimit time.Duration
}

// DuckDuckGoResponse represents the response from DuckDuckGo API
type DuckDuckGoResponse struct {
	Abstract       string                   `json:"Abstract"`
	AbstractText   string                   `json:"AbstractText"`
	AbstractSource string                   `json:"AbstractSource"`
	AbstractURL    string                   `json:"AbstractURL"`
	Image          string                   `json:"Image"`
	Heading        string                   `json:"Heading"`
	Answer         string                   `json:"Answer"`
	AnswerType     string                   `json:"AnswerType"`
	Definition     string                   `json:"Definition"`
	DefinitionURL  string                   `json:"DefinitionURL"`
	RelatedTopics  []DuckDuckGoRelatedTopic `json:"RelatedTopics"`
	Results        []DuckDuckGoResult       `json:"Results"`
	Type           string                   `json:"Type"`
}

type DuckDuckGoRelatedTopic struct {
	Result   string `json:"Result"`
	Icon     Icon   `json:"Icon"`
	FirstURL string `json:"FirstURL"`
	Text     string `json:"Text"`
}

type DuckDuckGoResult struct {
	Result   string `json:"Result"`
	FirstURL string `json:"FirstURL"`
	Text     string `json:"Text"`
}

type Icon struct {
	URL    string `json:"URL"`
	Height string `json:"Height"`
	Width  string `json:"Width"`
}

// NewDuckDuckGoProvider creates a new DuckDuckGo search provider
func NewDuckDuckGoProvider() *DuckDuckGoProvider {
	client := resty.New()
	client.SetTimeout(5 * time.Second) // Reduced timeout for better performance
	client.SetHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")

	return &DuckDuckGoProvider{
		client:    client,
		baseURL:   "https://api.duckduckgo.com/",
		rateLimit: 500 * time.Millisecond, // Faster rate limit
	}
}

// Name returns the provider name
func (d *DuckDuckGoProvider) Name() string {
	return "duckduckgo"
}

// Search performs a search using DuckDuckGo API
func (d *DuckDuckGoProvider) Search(request models.SearchRequest) ([]models.SearchResult, error) {
	params := url.Values{}
	params.Set("q", request.Query)
	params.Set("format", "json")
	params.Set("no_html", "1")
	params.Set("skip_disambig", "1")

	resp, err := d.client.R().
		SetQueryParamsFromValues(params).
		Get(d.baseURL)

	if err != nil {
		return nil, fmt.Errorf("duckduckgo search failed: %w", err)
	}

	var ddgResp DuckDuckGoResponse
	if err := json.Unmarshal(resp.Body(), &ddgResp); err != nil {
		return nil, fmt.Errorf("failed to parse duckduckgo response: %w", err)
	}

	return d.convertResults(ddgResp, request), nil
}

// convertResults converts DuckDuckGo response to our standard format
func (d *DuckDuckGoProvider) convertResults(resp DuckDuckGoResponse, request models.SearchRequest) []models.SearchResult {
	var results []models.SearchResult
	rank := 1

	// Add instant answer if available
	if resp.Answer != "" && resp.AnswerType != "" {
		results = append(results, models.SearchResult{
			ID:          d.generateID(resp.Answer),
			Title:       fmt.Sprintf("%s: %s", resp.AnswerType, resp.Answer),
			URL:         resp.AbstractURL,
			Description: resp.Answer,
			Source:      d.Name(),
			Rank:        rank,
			Score:       1.0,
			Timestamp:   time.Now(),
		})
		rank++
	}

	// Add abstract if available
	if resp.Abstract != "" {
		results = append(results, models.SearchResult{
			ID:          d.generateID(resp.Abstract),
			Title:       resp.Heading,
			URL:         resp.AbstractURL,
			Description: resp.Abstract,
			Source:      d.Name(),
			Rank:        rank,
			Score:       0.9,
			Timestamp:   time.Now(),
		})
		rank++
	}

	// Add related topics
	for _, topic := range resp.RelatedTopics {
		if topic.FirstURL != "" && topic.Text != "" {
			title := d.extractTitle(topic.Text)
			description := d.extractDescription(topic.Text)

			results = append(results, models.SearchResult{
				ID:          d.generateID(topic.FirstURL),
				Title:       title,
				URL:         topic.FirstURL,
				Description: description,
				Source:      d.Name(),
				Rank:        rank,
				Score:       0.8 - float64(rank)*0.05,
				Timestamp:   time.Now(),
			})
			rank++
		}
	}

	// Add direct results
	for _, result := range resp.Results {
		if result.FirstURL != "" && result.Text != "" {
			title := d.extractTitle(result.Text)
			description := d.extractDescription(result.Text)

			results = append(results, models.SearchResult{
				ID:          d.generateID(result.FirstURL),
				Title:       title,
				URL:         result.FirstURL,
				Description: description,
				Source:      d.Name(),
				Rank:        rank,
				Score:       0.7 - float64(rank)*0.05,
				Timestamp:   time.Now(),
			})
			rank++
		}
	}

	return results
}

// extractTitle extracts title from DuckDuckGo text format
func (d *DuckDuckGoProvider) extractTitle(text string) string {
	parts := strings.Split(text, " - ")
	if len(parts) > 0 {
		return strings.TrimSpace(parts[0])
	}
	return text
}

// extractDescription extracts description from DuckDuckGo text format
func (d *DuckDuckGoProvider) extractDescription(text string) string {
	parts := strings.Split(text, " - ")
	if len(parts) > 1 {
		return strings.TrimSpace(strings.Join(parts[1:], " - "))
	}
	return text
}

// generateID generates a unique ID for a result
func (d *DuckDuckGoProvider) generateID(content string) string {
	hash := md5.Sum([]byte(content))
	return fmt.Sprintf("ddg_%x", hash)[:16]
}

// IsAvailable checks if the provider is available
func (d *DuckDuckGoProvider) IsAvailable() bool {
	client := resty.New()
	client.SetTimeout(3 * time.Second)
	resp, err := client.R().Get(d.baseURL + "?q=test&format=json")
	return err == nil && resp.StatusCode() == 200
}

// GetRateLimit returns the rate limit for this provider
func (d *DuckDuckGoProvider) GetRateLimit() time.Duration {
	return d.rateLimit
}
