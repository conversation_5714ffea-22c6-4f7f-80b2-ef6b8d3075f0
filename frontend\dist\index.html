<!DOCTYPE html><html lang="en" class="scroll-smooth" data-astro-cid-sckkx6r4> <head><meta charset="UTF-8"><meta name="description" content="Ultra Search - Lightning fast meta search engine with 6 providers"><meta name="viewport" content="width=device-width, initial-scale=1.0"><link rel="icon" type="image/svg+xml" href="/favicon.svg"><meta name="generator" content="Astro v5.8.0"><title>Ultra Search</title><!-- Performance optimizations --><link rel="preconnect" href="http://localhost:8080"><link rel="dns-prefetch" href="http://localhost:8080"><meta name="theme-color" content="#3b82f6"><!-- PWA meta tags --><meta name="apple-mobile-web-app-capable" content="yes"><meta name="apple-mobile-web-app-status-bar-style" content="default"><meta name="apple-mobile-web-app-title" content="Ultra Search"><!-- Open Graph --><meta property="og:title" content="Ultra Search"><meta property="og:description" content="Ultra Search - Lightning fast meta search engine with 6 providers"><meta property="og:type" content="website"><!-- Preload critical fonts --><link rel="preload" href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" as="style" onload="this.onload=null;this.rel='stylesheet'"><!-- Critical CSS for instant loading --><link rel="stylesheet" href="/assets/index.DU4iybpo.css"></head> <body class="min-h-screen bg-gradient-to-br from-gray-50 via-white to-blue-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 text-gray-900 dark:text-gray-100 transition-colors duration-300" data-astro-cid-sckkx6r4> <!-- Background pattern --> <div class="fixed inset-0 -z-10 opacity-30" data-astro-cid-sckkx6r4> <div class="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(59,130,246,0.1),transparent_50%)]" data-astro-cid-sckkx6r4></div> <div class="absolute inset-0 bg-[radial-gradient(circle_at_80%_20%,rgba(168,85,247,0.1),transparent_50%)]" data-astro-cid-sckkx6r4></div> <div class="absolute inset-0 bg-[radial-gradient(circle_at_20%_80%,rgba(34,197,94,0.1),transparent_50%)]" data-astro-cid-sckkx6r4></div> </div>  <main class="min-h-screen flex flex-col"> <!-- Main content area --> <div class="flex-1 container mx-auto px-4 py-8 max-w-6xl"> <header class="text-center mb-12 search-container"> <div class="inline-flex items-center gap-3 mb-6"> <!-- Logo icon --> <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg"> <svg class="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2.5" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path> </svg> </div> <!-- Brand name --> <h1 class="text-5xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-blue-800 bg-clip-text text-transparent dark:from-blue-400 dark:via-purple-400 dark:to-blue-300">
Ultra Search
</h1> </div> <!-- Tagline --> <p class="text-lg text-gray-600 dark:text-gray-400 font-medium mb-2">
Lightning-fast meta search across 6 providers
</p> <!-- Feature badges --> <div class="flex flex-wrap justify-center gap-2 mb-8"> <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"> <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20"> <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path> </svg>
6 Providers
</span> <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"> <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20"> <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path> </svg>
No Tracking
</span> <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200"> <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20"> <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path> </svg>
Open Source
</span> </div> </header> <div class="max-w-3xl mx-auto mb-12 search-container"> <!-- Main search form --> <form id="search-form" class="relative mb-6"> <div class="relative group"> <!-- Search input with modern styling --> <input type="text" id="search-input" placeholder="Search anything..." class="w-full px-6 py-4 pr-16 text-lg border-2 border-gray-200 dark:border-gray-600 rounded-2xl focus:outline-none focus:ring-4 focus:ring-blue-500/20 focus:border-blue-500 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 transition-all duration-300 shadow-lg hover:shadow-xl group-hover:border-gray-300 dark:group-hover:border-gray-500" autocomplete="off" spellcheck="false"> <!-- Search button --> <button type="submit" class="absolute right-2 top-1/2 transform -translate-y-1/2 p-3 text-gray-500 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200 rounded-xl hover:bg-gray-100 dark:hover:bg-gray-700" aria-label="Search"> <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2.5" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path> </svg> </button> <!-- Clear button (hidden by default) --> <button type="button" id="clear-search" class="absolute right-14 top-1/2 transform -translate-y-1/2 p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors duration-200 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 hidden" aria-label="Clear search"> <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path> </svg> </button> </div> <!-- Enhanced autocomplete with modern styling --> <div id="autocomplete" class="absolute top-full left-0 right-0 bg-white/95 dark:bg-gray-800/95 backdrop-blur-md border border-gray-200 dark:border-gray-600 rounded-2xl shadow-2xl mt-2 hidden z-50 overflow-hidden"> <div class="p-2"> <!-- Recent searches --> <div id="recent-searches" class="hidden"> <div class="px-3 py-2 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wide">Recent Searches</div> <div id="recent-list"></div> </div> <!-- Suggestions --> <div id="suggestions-list"></div> <!-- Quick actions --> <div class="border-t border-gray-100 dark:border-gray-700 mt-2 pt-2"> <div class="px-3 py-2 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wide">Quick Actions</div> <button type="button" class="w-full text-left px-3 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors duration-150 flex items-center gap-2"> <svg class="w-4 h-4 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path> </svg>
I'm Feeling Lucky
</button> </div> </div> </div> </form> <!-- Search options and filters --> <div class="flex flex-wrap items-center justify-center gap-3 mb-6"> <!-- Search type filters --> <div class="flex items-center gap-2"> <span class="text-sm font-medium text-gray-600 dark:text-gray-400">Search:</span> <div class="flex rounded-xl bg-gray-100 dark:bg-gray-800 p-1"> <button type="button" class="search-filter active px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200" data-filter="all">
All
</button> <button type="button" class="search-filter px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200" data-filter="web">
Web
</button> <button type="button" class="search-filter px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200" data-filter="images">
Images
</button> <button type="button" class="search-filter px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200" data-filter="news">
News
</button> </div> </div> <!-- Time filter --> <div class="flex items-center gap-2"> <span class="text-sm font-medium text-gray-600 dark:text-gray-400">Time:</span> <select id="time-filter" class="px-3 py-2 text-sm border border-gray-200 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500"> <option value="any">Any time</option> <option value="day">Past 24 hours</option> <option value="week">Past week</option> <option value="month">Past month</option> <option value="year">Past year</option> </select> </div> </div> <!-- Action buttons --> <div class="flex justify-center gap-4"> <button type="submit" form="search-form" class="btn btn-primary px-8 py-3 text-base font-semibold shadow-lg hover:shadow-xl"> <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path> </svg>
Ultra Search
</button> <button type="button" id="lucky-search" class="btn btn-secondary px-8 py-3 text-base font-semibold shadow-lg hover:shadow-xl"> <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path> </svg>
I'm Feeling Lucky
</button> </div> </div>  <div id="loading" class="hidden text-center py-12 fade-in"> <!-- Modern loading animation --> <div class="flex justify-center items-center mb-6"> <div class="relative"> <!-- Outer ring --> <div class="w-16 h-16 border-4 border-blue-200 dark:border-blue-800 rounded-full animate-pulse"></div> <!-- Inner spinning ring --> <div class="absolute top-0 left-0 w-16 h-16 border-4 border-transparent border-t-blue-600 dark:border-t-blue-400 rounded-full animate-spin"></div> <!-- Center dot --> <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-3 h-3 bg-blue-600 dark:bg-blue-400 rounded-full animate-pulse"></div> </div> </div> <!-- Loading text with typing animation --> <div class="space-y-3"> <p class="text-lg font-medium text-gray-800 dark:text-gray-200"> <span id="loading-text">Searching</span> <span class="inline-flex"> <span class="w-1 h-5 bg-blue-600 dark:bg-blue-400 rounded-full pulse-dot"></span> <span class="w-1 h-5 bg-blue-600 dark:bg-blue-400 rounded-full pulse-dot ml-1"></span> <span class="w-1 h-5 bg-blue-600 dark:bg-blue-400 rounded-full pulse-dot ml-1"></span> </span> </p> <!-- Progress indicators --> <div class="max-w-md mx-auto"> <div class="flex justify-between text-xs text-gray-500 dark:text-gray-400 mb-2"> <span>Querying providers</span> <span id="progress-text">0%</span> </div> <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 overflow-hidden"> <div id="progress-bar" class="h-2 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full transition-all duration-500 ease-out" style="width: 0%"></div> </div> </div> <!-- Provider status indicators --> <div id="provider-status" class="flex flex-wrap justify-center gap-2 mt-4"> <!-- Provider indicators will be populated by JavaScript --> </div> </div> </div> <!-- Skeleton loading for results --> <div id="skeleton-loading" class="hidden max-w-2xl mx-auto space-y-6 py-8"> <div class="animate-pulse"> <!-- Search info skeleton --> <div class="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/3 mb-6"></div> <!-- Result skeletons --> <div class="space-y-6"> <div class="space-y-3" v-for="i in 3"> <div class="h-3 bg-gray-200 dark:bg-gray-700 rounded w-1/4"></div> <div class="h-5 bg-gray-200 dark:bg-gray-700 rounded w-3/4"></div> <div class="space-y-2"> <div class="h-3 bg-gray-200 dark:bg-gray-700 rounded"></div> <div class="h-3 bg-gray-200 dark:bg-gray-700 rounded w-5/6"></div> </div> <div class="h-3 bg-gray-200 dark:bg-gray-700 rounded w-1/3"></div> </div> </div> </div> </div> <script type="module">let g,r;const a=["Searching","Querying providers","Aggregating results","Ranking content","Almost done"];function i(){const t=document.getElementById("loading-text"),e=document.getElementById("progress-bar"),s=document.getElementById("progress-text");let o=0,n=0;g=setInterval(()=>{t&&(t.textContent=a[o],o=(o+1)%a.length)},1500),r=setInterval(()=>{n=Math.min(n+Math.random()*15+5,95),e&&(e.style.width=`${n}%`),s&&(s.textContent=`${Math.round(n)}%`),n>=95&&clearInterval(r)},300)}function l(){clearInterval(g),clearInterval(r);const t=document.getElementById("progress-bar"),e=document.getElementById("progress-text");t&&(t.style.width="100%"),e&&(e.textContent="100%")}window.loadingAnimation={start:i,stop:l};</script> <!-- Search Info --><div id="search-info" class="max-w-2xl mx-auto mb-4 text-sm text-gray-600 dark:text-gray-400 hidden"> <!-- Search metadata --> </div> <!-- Search Results --> <div id="search-results" class="max-w-2xl mx-auto"> <!-- Results populated by JavaScript --> </div> <!-- Pagination --> <div id="pagination" class="max-w-2xl mx-auto mt-8 flex justify-center gap-2 hidden"> <!-- Pagination controls --> </div> </div> <!-- Footer --> <footer class="mt-auto py-8 border-t border-gray-200 dark:border-gray-700 bg-white/50 dark:bg-gray-900/50 backdrop-blur-sm"> <div class="container mx-auto px-4 max-w-6xl"> <div class="flex flex-col md:flex-row items-center justify-between gap-4"> <div class="flex items-center gap-6 text-sm text-gray-600 dark:text-gray-400"> <span>© 2024 Ultra Search</span> <a href="#" class="hover:text-blue-600 dark:hover:text-blue-400 transition-colors">Privacy</a> <a href="#" class="hover:text-blue-600 dark:hover:text-blue-400 transition-colors">Terms</a> <a href="#" class="hover:text-blue-600 dark:hover:text-blue-400 transition-colors">About</a> </div> <div class="flex items-center gap-4"> <!-- Provider status indicators --> <div class="flex items-center gap-2 text-xs text-gray-500 dark:text-gray-400"> <span>Powered by:</span> <div class="flex items-center gap-1"> <div class="w-2 h-2 rounded-full bg-green-500" title="Google"></div> <div class="w-2 h-2 rounded-full bg-orange-500" title="Bing"></div> <div class="w-2 h-2 rounded-full bg-red-500" title="DuckDuckGo"></div> <div class="w-2 h-2 rounded-full bg-purple-500" title="Yahoo"></div> <div class="w-2 h-2 rounded-full bg-green-600" title="Startpage"></div> <div class="w-2 h-2 rounded-full bg-orange-600" title="Brave"></div> </div> </div> <!-- Social links --> <div class="flex items-center gap-2"> <a href="#" class="p-2 text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors" title="GitHub"> <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24"> <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"></path> </svg> </a> <a href="#" class="p-2 text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors" title="Twitter"> <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24"> <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"></path> </svg> </a> </div> </div> </div> </div> </footer> </main> <script type="module" src="/assets/index.astro_astro_type_script_index_0_lang.DSDJ94nx.js"></script>  <!-- Theme toggle button --> <button id="theme-toggle" class="fixed bottom-6 right-6 p-3 bg-white dark:bg-gray-800 rounded-full shadow-lg hover:shadow-xl transition-all duration-200 border border-gray-200 dark:border-gray-700 hover-lift z-50" aria-label="Toggle theme" data-astro-cid-sckkx6r4> <svg class="w-5 h-5 text-gray-600 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-astro-cid-sckkx6r4> <path class="sun" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" data-astro-cid-sckkx6r4></path> <path class="moon hidden" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" data-astro-cid-sckkx6r4></path> </svg> </button> <!-- Scroll to top button --> <button id="scroll-top" class="fixed bottom-6 left-6 p-3 bg-blue-600 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-200 hover:bg-blue-700 hover-lift z-50 opacity-0 pointer-events-none" aria-label="Scroll to top" data-astro-cid-sckkx6r4> <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-astro-cid-sckkx6r4> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18" data-astro-cid-sckkx6r4></path> </svg> </button> <script type="module">const t=document.getElementById("theme-toggle"),n=t?.querySelector(".sun"),s=t?.querySelector(".moon");function c(){const o=document.documentElement.classList.contains("dark");n&&s&&(n.classList.toggle("hidden",o),s.classList.toggle("hidden",!o))}t?.addEventListener("click",()=>{document.documentElement.classList.toggle("dark"),localStorage.setItem("theme",document.documentElement.classList.contains("dark")?"dark":"light"),c()});(localStorage.getItem("theme")==="dark"||!localStorage.getItem("theme")&&window.matchMedia("(prefers-color-scheme: dark)").matches)&&document.documentElement.classList.add("dark");c();const e=document.getElementById("scroll-top");window.addEventListener("scroll",()=>{e&&(window.scrollY>300?e.classList.remove("opacity-0","pointer-events-none"):e.classList.add("opacity-0","pointer-events-none"))});e?.addEventListener("click",()=>{window.scrollTo({top:0,behavior:"smooth"})});</script> </body> </html>