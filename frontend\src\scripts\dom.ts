// DOM utilities - cached selectors for performance
export class DOMCache {
  private static cache = new Map<string, Element | null>();

  static get<T extends Element>(selector: string): T | null {
    if (!this.cache.has(selector)) {
      this.cache.set(selector, document.querySelector(selector));
    }
    return this.cache.get(selector) as T | null;
  }

  static getAll<T extends Element>(selector: string): NodeListOf<T> {
    return document.querySelectorAll(selector);
  }

  static clear(): void {
    this.cache.clear();
  }
}

// DOM element getters with proper typing
export const elements = {
  get searchForm(): HTMLFormElement | null {
    return DOMCache.get<HTMLFormElement>('#search-form');
  },
  
  get searchInput(): HTMLInputElement | null {
    return DOMCache.get<HTMLInputElement>('#search-input');
  },
  
  get autocomplete(): HTMLDivElement | null {
    return DOMCache.get<HTMLDivElement>('#autocomplete');
  },
  
  get loading(): HTMLDivElement | null {
    return DOMCache.get<HTMLDivElement>('#loading');
  },
  
  get results(): HTMLDivElement | null {
    return DOMCache.get<HTMLDivElement>('#search-results');
  },
  
  get searchInfo(): HTMLDivElement | null {
    return DOMCache.get<HTMLDivElement>('#search-info');
  },
  
  get pagination(): HTMLDivElement | null {
    return DOMCache.get<HTMLDivElement>('#pagination');
  },
  
  get quickSearchButtons(): NodeListOf<HTMLButtonElement> {
    return DOMCache.getAll<HTMLButtonElement>('.quick-search');
  }
};
