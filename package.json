{"name": "ultra-search", "version": "1.0.0", "description": "Ultra Search - Meta Search Engine", "scripts": {"dev": "bun dev.js", "build": "bun build.js", "start": "bun run build && ./ultra_search.exe", "start:prod": "./ultra_search.exe", "start:dev": "./ultra_search.exe --dev", "dev:backend": "bun dev-watch.js", "dev:frontend": "cd frontend && bun run dev", "build:backend": "go build -o ultra_search.exe ./cmd/server", "build:frontend": "cd frontend && bun run build", "test": "go test ./... && cd frontend && bun run build", "clean": "rm -rf frontend/dist/ frontend/node_modules/.cache/", "docker:build": "docker-compose build", "docker:up": "docker-compose up", "docker:down": "docker-compose down"}, "dependencies": {"@astrojs/tailwind": "^6.0.2", "tailwindcss": "^4.1.7"}, "devDependencies": {"concurrently": "^8.2.2"}, "keywords": ["search", "meta-search", "go", "astro", "web-scraping"], "author": "Ultra Search Team", "license": "MIT"}