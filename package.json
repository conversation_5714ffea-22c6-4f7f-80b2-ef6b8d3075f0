{"name": "ultra-search", "version": "1.0.0", "description": "Ultra Search - Meta Search Engine", "scripts": {"dev": "node start.js", "dev:old": "concurrently --kill-others --prefix-colors \"bgBlue.bold,bgGreen.bold\" --names \"BACKEND,FRONTEND\" \"bun run dev:backend\" \"bun run dev:frontend\"", "dev:backend": "node dev-watch.js", "dev:frontend": "cd frontend && bun run dev", "build": "bun run build:backend && bun run build:frontend", "build:backend": "go build -o bin/ultra-search ./cmd/server", "build:frontend": "cd frontend && bun run build", "start": "bun run build && ./bin/ultra-search", "test": "go test ./... && cd frontend && bun run build", "clean": "rm -rf bin/ frontend/dist/ frontend/node_modules/.cache/", "docker:build": "docker-compose build", "docker:up": "docker-compose up", "docker:down": "docker-compose down"}, "dependencies": {"@astrojs/tailwind": "^6.0.2", "tailwindcss": "^4.1.7"}, "devDependencies": {"concurrently": "^8.2.2"}, "keywords": ["search", "meta-search", "go", "astro", "web-scraping"], "author": "Ultra Search Team", "license": "MIT"}