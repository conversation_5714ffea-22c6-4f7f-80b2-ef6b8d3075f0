// API utilities - lightweight and focused
import type { SearchResponse, AutocompleteResponse } from './types.ts';

const API_BASE = 'http://localhost:8080/api/v1';

export class SearchAPI {
  static async search(query: string, page: number = 1): Promise<SearchResponse> {
    const response = await fetch(
      `${API_BASE}/search?q=${encodeURIComponent(query)}&page=${page}&per_page=10`
    );

    if (!response.ok) {
      throw new Error(`Search failed: ${response.status}`);
    }

    return response.json();
  }

  static async autocomplete(query: string): Promise<AutocompleteResponse> {
    const response = await fetch(
      `${API_BASE}/autocomplete?q=${encodeURIComponent(query)}&limit=5`
    );

    if (!response.ok) {
      throw new Error(`Autocomplete failed: ${response.status}`);
    }

    return response.json();
  }
}
