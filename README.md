# Ultra Search - Meta Search Engine

A comprehensive meta search engine that aggregates results from multiple search providers (Google, Bing, DuckDuckGo) with intelligent deduplication, ranking algorithms, and a clean, responsive interface.

## Features

### Backend (Go)
- **High-performance concurrent search aggregation** from multiple providers
- **Intelligent result deduplication** and ranking algorithms
- **RESTful API endpoints** for search queries and autocomplete
- **Advanced caching mechanisms** with intelligent cache management
- **Rate limiting** and error handling for external API calls
- **Modular architecture** for easy addition of new search providers
- **Environment-based configuration**
- **Comprehensive performance monitoring** and real-time metrics
- **Enhanced web scraping** with multiple fallback strategies
- **Quality filtering** and spam detection
- **Provider performance ranking** and adaptive optimization

### Frontend (Astro)
- **Clean, responsive search interface** built with Tailwind CSS
- **Real-time search suggestions** and autocomplete
- **Aggregated results display** with source attribution
- **Filtering and sorting capabilities**
- **Fast page loads** with minimal JavaScript bundle
- **Progressive enhancement** and accessibility features

### Infrastructure
- **Docker containerization** for easy deployment
- **Docker Compose** for orchestration
- **Health checks** and monitoring
- **Production-ready configuration**

## Quick Start

### Prerequisites
- Go 1.21 or later
- Bun (for frontend development)
- Docker and Docker Compose (for containerized deployment)

### Development Setup

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd ultra_search
   ```

2. **Set up environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your preferred settings
   ```

3. **Run the backend**
   ```bash
   go mod download
   go run cmd/server/main.go
   ```

4. **Run the frontend** (in a separate terminal)
   ```bash
   cd frontend
   bun install
   bun run dev
   ```

5. **Access the application**
   - Frontend: http://localhost:3000
   - Backend API: http://localhost:8080

### Docker Deployment

1. **Build and run with Docker Compose**
   ```bash
   docker-compose up --build
   ```

2. **Access the application**
   - Frontend: http://localhost:3000
   - Backend API: http://localhost:8080

## API Endpoints

### Search
```
GET /api/v1/search?q=query&page=1&per_page=10
```

### Autocomplete
```
GET /api/v1/autocomplete?q=query&limit=5
```

### Health Check
```
GET /api/v1/health
```

### Statistics
```
GET /api/v1/stats
```

### Clear Cache
```
POST /api/v1/cache/clear?type=all
```

## Configuration

The application uses environment variables for configuration. See `.env.example` for all available options.

### Key Configuration Options

- `MAX_CONCURRENT_PROVIDERS`: Number of search providers to query simultaneously
- `SEARCH_TIMEOUT`: Maximum time to wait for search results
- `CACHE_TTL`: How long to cache search results
- `RATE_LIMIT_RPM`: Requests per minute limit per IP
- `ENABLED_PROVIDERS`: Comma-separated list of search providers to use

## Architecture

### Backend Structure
```
backend/
├── cmd/server/          # Application entry point
├── internal/
│   ├── api/            # HTTP handlers and middleware
│   ├── search/         # Search provider implementations
│   ├── aggregator/     # Result aggregation logic
│   ├── cache/          # Caching mechanisms
│   ├── config/         # Configuration management
│   └── models/         # Data structures
```

### Search Providers

The application currently includes:

1. **DuckDuckGo Provider** - Uses DuckDuckGo's instant answer API
2. **Bing Provider** - Enhanced web scraping with intelligent parsing
3. **Google Provider** - Advanced web scraping with multiple fallback strategies
4. **Yahoo Provider** - Real-time web scraping with content filtering
5. **Startpage Provider** - Privacy-focused search with Google results
6. **Brave Provider** - Independent search engine with quality scoring

### Adding New Search Providers

1. Implement the `SearchProvider` interface in `internal/models/search.go`
2. Create a new provider file in `internal/search/`
3. Register the provider in `cmd/server/main.go`
4. Add configuration options as needed

## Production Considerations

### API Keys
For production use, replace the mock implementations with official APIs:

- **Google**: Use Google Custom Search API
- **Bing**: Use Bing Search API v7

### Monitoring
- Health checks are available at `/api/v1/health`
- Metrics can be collected from `/api/v1/stats`
- Consider adding structured logging and monitoring tools

### Scaling
- The application is stateless and can be horizontally scaled
- Consider using Redis for distributed caching
- Implement database storage for analytics and user preferences

### Security
- Rate limiting is implemented per IP
- CORS headers are configurable
- Security headers are included
- Consider adding authentication for admin endpoints

## Development

### Running Tests
```bash
go test ./...
```

### Building for Production
```bash
# Backend
go build -o ultra-search ./cmd/server

# Frontend
cd frontend
bun run build
```

### Code Structure
- Follow Go best practices and project layout standards
- Use dependency injection for testability
- Implement proper error handling and logging
- Write comprehensive tests for critical functionality

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## License

[Add your license here]

## Support

For issues and questions, please use the GitHub issue tracker.
