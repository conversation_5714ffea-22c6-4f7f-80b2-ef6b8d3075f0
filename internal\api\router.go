package api

import (
	"net/http/httputil"
	"net/url"
	"os"
	"path/filepath"
	"strings"
	"ultra_search/internal/aggregator"
	"ultra_search/internal/cache"
	"ultra_search/internal/config"
	"ultra_search/internal/metrics"

	"github.com/gin-gonic/gin"
)

// SetupRouter configures and returns the Gin router
func SetupRouter(agg *aggregator.SearchAggregator, cm *cache.CacheManager, cfg *config.Config, m *metrics.PerformanceMetrics, devMode bool) *gin.Engine {
	// Set Gin mode based on environment
	if cfg.Logging.Level == "debug" {
		gin.SetMode(gin.DebugMode)
	} else {
		gin.SetMode(gin.ReleaseMode)
	}

	router := gin.New()

	// Add middleware
	router.Use(gin.Recovery())
	router.Use(LoggingMiddleware())
	router.Use(SecurityMiddleware())
	router.Use(CORSMiddleware(&cfg.Server.CORS))
	router.Use(RateLimitMiddleware(&cfg.RateLimit))
	router.Use(MetricsMiddleware())

	// Create handlers
	searchHandler := NewSearchHandler(agg, cm, cfg, m)

	// API routes
	api := router.Group("/api/v1")
	{
		// Search endpoints
		api.GET("/search", searchHandler.Search)
		api.GET("/autocomplete", searchHandler.Autocomplete)

		// System endpoints
		api.GET("/health", searchHandler.Health)
		api.GET("/stats", searchHandler.Stats)
		api.POST("/cache/clear", searchHandler.ClearCache)
	}

	// Frontend serving
	if devMode {
		// Development mode: proxy to Astro dev server
		setupDevProxy(router)
	} else {
		// Production mode: serve static files
		setupStaticFiles(router)
	}

	return router
}

// setupDevProxy sets up a proxy to the Astro dev server for development mode
func setupDevProxy(router *gin.Engine) {
	// Proxy to Astro dev server running on port 3000
	target, _ := url.Parse("http://localhost:3000")
	proxy := httputil.NewSingleHostReverseProxy(target)

	// Handle all non-API routes by proxying to Astro dev server
	router.NoRoute(func(c *gin.Context) {
		// Skip API routes
		if strings.HasPrefix(c.Request.URL.Path, "/api/") {
			c.JSON(404, gin.H{"error": "API endpoint not found"})
			return
		}

		// Proxy to Astro dev server
		proxy.ServeHTTP(c.Writer, c.Request)
	})
}

// setupStaticFiles serves the built frontend files for production mode
func setupStaticFiles(router *gin.Engine) {
	// Serve static files from frontend/dist
	distPath := "frontend/dist"

	// Check if dist directory exists
	if _, err := os.Stat(distPath); os.IsNotExist(err) {
		// Fallback: serve a simple message
		router.NoRoute(func(c *gin.Context) {
			if strings.HasPrefix(c.Request.URL.Path, "/api/") {
				c.JSON(404, gin.H{"error": "API endpoint not found"})
				return
			}

			c.HTML(200, "", `
<!DOCTYPE html>
<html>
<head>
    <title>Ultra Search</title>
    <style>
        body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
        .container { max-width: 600px; margin: 0 auto; }
        .error { color: #e74c3c; }
        .info { color: #3498db; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Ultra Search</h1>
        <p class="error">Frontend not built yet!</p>
        <p class="info">Run the following commands to build the frontend:</p>
        <pre>cd frontend && bun run build</pre>
        <p>Or run in development mode:</p>
        <pre>./ultra_search --dev</pre>
        <p>API is available at <a href="/api/v1/health">/api/v1/health</a></p>
    </div>
</body>
</html>
			`)
		})
		return
	}

	// Serve static files
	router.Static("/assets", filepath.Join(distPath, "assets"))
	router.StaticFile("/favicon.svg", filepath.Join(distPath, "favicon.svg"))

	// Serve index.html for all non-API routes (SPA routing)
	router.NoRoute(func(c *gin.Context) {
		// Skip API routes
		if strings.HasPrefix(c.Request.URL.Path, "/api/") {
			c.JSON(404, gin.H{"error": "API endpoint not found"})
			return
		}

		// Serve index.html for SPA routing
		c.File(filepath.Join(distPath, "index.html"))
	})
}
