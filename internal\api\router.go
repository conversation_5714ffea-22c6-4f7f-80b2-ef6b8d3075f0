package api

import (
	"ultra_search/internal/aggregator"
	"ultra_search/internal/cache"
	"ultra_search/internal/config"
	"ultra_search/internal/metrics"

	"github.com/gin-gonic/gin"
)

// SetupRouter configures and returns the Gin router
func SetupRouter(agg *aggregator.SearchAggregator, cm *cache.CacheManager, cfg *config.Config, m *metrics.PerformanceMetrics) *gin.Engine {
	// Set Gin mode based on environment
	if cfg.Logging.Level == "debug" {
		gin.SetMode(gin.DebugMode)
	} else {
		gin.SetMode(gin.ReleaseMode)
	}

	router := gin.New()

	// Add middleware
	router.Use(gin.Recovery())
	router.Use(LoggingMiddleware())
	router.Use(SecurityMiddleware())
	router.Use(CORSMiddleware(&cfg.Server.CORS))
	router.Use(RateLimitMiddleware(&cfg.RateLimit))
	router.Use(MetricsMiddleware())

	// Create handlers
	searchHandler := NewSearchHandler(agg, cm, cfg, m)

	// API routes
	api := router.Group("/api/v1")
	{
		// Search endpoints
		api.GET("/search", searchHandler.Search)
		api.GET("/autocomplete", searchHandler.Autocomplete)

		// System endpoints
		api.GET("/health", searchHandler.Health)
		api.GET("/stats", searchHandler.Stats)
		api.POST("/cache/clear", searchHandler.ClearCache)
	}

	// Root endpoint
	router.GET("/", func(c *gin.Context) {
		c.JSON(200, gin.H{
			"service": "Ultra Search API",
			"version": "1.0.0",
			"endpoints": gin.H{
				"search":       "/api/v1/search?q=query",
				"autocomplete": "/api/v1/autocomplete?q=query",
				"health":       "/api/v1/health",
				"stats":        "/api/v1/stats",
				"clear_cache":  "/api/v1/cache/clear",
			},
		})
	})

	return router
}
