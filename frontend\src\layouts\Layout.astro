---
export interface Props {
  title: string;
  description?: string;
}

const { title, description = "Ultra Search - Lightning fast meta search engine with 6 providers" } = Astro.props;
---

<!doctype html>
<html lang="en" class="scroll-smooth">
  <head>
    <meta charset="UTF-8" />
    <meta name="description" content={description} />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <meta name="generator" content={Astro.generator} />
    <title>{title}</title>

    <!-- Performance optimizations -->
    <link rel="preconnect" href="http://localhost:8080" />
    <link rel="dns-prefetch" href="http://localhost:8080" />
    <meta name="theme-color" content="#3b82f6" />

    <!-- PWA meta tags -->
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="default" />
    <meta name="apple-mobile-web-app-title" content="Ultra Search" />

    <!-- Open Graph -->
    <meta property="og:title" content={title} />
    <meta property="og:description" content={description} />
    <meta property="og:type" content="website" />

    <!-- No external fonts to avoid CSP issues -->
  </head>
  <body class="min-h-screen bg-gradient-to-br from-gray-50 via-white to-blue-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 text-gray-900 dark:text-gray-100 transition-colors duration-300">
    <!-- Background pattern -->
    <div class="fixed inset-0 -z-10 opacity-30">
      <div class="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(59,130,246,0.1),transparent_50%)]"></div>
      <div class="absolute inset-0 bg-[radial-gradient(circle_at_80%_20%,rgba(168,85,247,0.1),transparent_50%)]"></div>
      <div class="absolute inset-0 bg-[radial-gradient(circle_at_20%_80%,rgba(34,197,94,0.1),transparent_50%)]"></div>
    </div>

    <slot />

    <!-- Theme toggle button -->
    <button id="theme-toggle" class="fixed bottom-6 right-6 p-3 bg-white dark:bg-gray-800 rounded-full shadow-lg hover:shadow-xl transition-all duration-200 border border-gray-200 dark:border-gray-700 hover-lift z-50" aria-label="Toggle theme">
      <svg class="w-5 h-5 text-gray-600 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path class="sun" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"></path>
        <path class="moon hidden" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"></path>
      </svg>
    </button>

    <!-- Scroll to top button -->
    <button id="scroll-top" class="fixed bottom-6 left-6 p-3 bg-blue-600 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-200 hover:bg-blue-700 hover-lift z-50 opacity-0 pointer-events-none" aria-label="Scroll to top">
      <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18"></path>
      </svg>
    </button>

    <script>
      // Theme toggle functionality
      const themeToggle = document.getElementById('theme-toggle');
      const sunIcon = themeToggle?.querySelector('.sun');
      const moonIcon = themeToggle?.querySelector('.moon');

      function updateTheme() {
        const isDark = document.documentElement.classList.contains('dark');
        if (sunIcon && moonIcon) {
          sunIcon.classList.toggle('hidden', isDark);
          moonIcon.classList.toggle('hidden', !isDark);
        }
      }

      themeToggle?.addEventListener('click', () => {
        document.documentElement.classList.toggle('dark');
        localStorage.setItem('theme', document.documentElement.classList.contains('dark') ? 'dark' : 'light');
        updateTheme();
      });

      // Initialize theme
      if (localStorage.getItem('theme') === 'dark' || (!localStorage.getItem('theme') && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
        document.documentElement.classList.add('dark');
      }
      updateTheme();

      // Scroll to top functionality
      const scrollTopBtn = document.getElementById('scroll-top');

      window.addEventListener('scroll', () => {
        if (scrollTopBtn) {
          if (window.scrollY > 300) {
            scrollTopBtn.classList.remove('opacity-0', 'pointer-events-none');
          } else {
            scrollTopBtn.classList.add('opacity-0', 'pointer-events-none');
          }
        }
      });

      scrollTopBtn?.addEventListener('click', () => {
        window.scrollTo({ top: 0, behavior: 'smooth' });
      });
    </script>
  </body>
</html>
