---
export interface Props {
  title: string;
  description?: string;
}

const { title, description = "Ultra Search - Lightning fast meta search engine with 6 providers" } = Astro.props;
---

<!doctype html>
<html lang="en" class="scroll-smooth">
  <head>
    <meta charset="UTF-8" />
    <meta name="description" content={description} />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <meta name="generator" content={Astro.generator} />
    <title>{title}</title>

    <!-- Performance optimizations -->
    <link rel="preconnect" href="http://localhost:8080" />
    <link rel="dns-prefetch" href="http://localhost:8080" />
    <meta name="theme-color" content="#0066ff" />

    <!-- Minimal critical CSS -->
    <style>
      body {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      }
    </style>
  </head>
  <body class="min-h-screen bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100">
    <slot />
  </body>
</html>
