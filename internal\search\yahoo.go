package search

import (
	"crypto/md5"
	"fmt"
	"net/url"
	"regexp"
	"strings"
	"time"

	"ultra_search/internal/models"

	"github.com/go-resty/resty/v2"
)

// YahooProvider implements SearchProvider for Yahoo Search
type YahooProvider struct {
	client    *resty.Client
	baseURL   string
	rateLimit time.Duration
}

// NewYahooProvider creates a new Yahoo search provider
func NewYahooProvider() *YahooProvider {
	client := resty.New()
	client.SetTimeout(10 * time.Second)
	client.SetHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
	client.SetHeader("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8")
	client.SetHeader("Accept-Language", "en-US,en;q=0.5")
	client.SetHeader("Accept-Encoding", "gzip, deflate")
	client.SetHeader("DNT", "1")
	client.SetHeader("Connection", "keep-alive")
	client.SetHeader("Upgrade-Insecure-Requests", "1")

	return &YahooProvider{
		client:    client,
		baseURL:   "https://search.yahoo.com/search",
		rateLimit: 1500 * time.Millisecond, // Conservative rate limit
	}
}

// Name returns the provider name
func (y *YahooProvider) Name() string {
	return "yahoo"
}

// IsAvailable checks if the provider is available
func (y *YahooProvider) IsAvailable() bool {
	return true
}

// GetRateLimit returns the rate limit for this provider
func (y *YahooProvider) GetRateLimit() time.Duration {
	return y.rateLimit
}

// Search performs a search using Yahoo web scraping
func (y *YahooProvider) Search(request models.SearchRequest) ([]models.SearchResult, error) {
	// Build search URL
	params := url.Values{}
	params.Set("p", request.Query)
	params.Set("n", "20") // Request more results
	if request.Language != "" {
		params.Set("vl", "lang_"+request.Language)
	}
	if request.Region != "" {
		params.Set("vc", request.Region)
	}
	if request.SafeSearch {
		params.Set("vm", "r") // Restricted mode
	}

	searchURL := y.baseURL + "?" + params.Encode()

	// Make the request
	resp, err := y.client.R().Get(searchURL)
	if err != nil {
		return nil, fmt.Errorf("yahoo search request failed: %w", err)
	}

	if resp.StatusCode() != 200 {
		return nil, fmt.Errorf("yahoo search returned status %d", resp.StatusCode())
	}

	// Parse results
	results := y.parseYahooResults(string(resp.Body()), request)
	return results, nil
}

// parseYahooResults parses Yahoo search results from HTML
func (y *YahooProvider) parseYahooResults(html string, request models.SearchRequest) []models.SearchResult {
	var results []models.SearchResult

	// Yahoo uses specific class names for search results
	// Primary regex for Yahoo organic results
	resultRegex := regexp.MustCompile(`<div[^>]*class="[^"]*algo[^"]*"[^>]*>.*?<h3[^>]*class="[^"]*title[^"]*"[^>]*>.*?<a[^>]*href="([^"]*)"[^>]*[^>]*>(.*?)</a>.*?</h3>.*?<p[^>]*class="[^"]*compText[^"]*"[^>]*>(.*?)</p>`)

	// Alternative regex for different Yahoo layouts
	altResultRegex := regexp.MustCompile(`<div[^>]*class="[^"]*Sr[^"]*"[^>]*>.*?<h3[^>]*>.*?<a[^>]*href="([^"]*)"[^>]*>(.*?)</a>.*?</h3>.*?<div[^>]*class="[^"]*compText[^"]*"[^>]*>(.*?)</div>`)

	// Try primary regex first
	matches := resultRegex.FindAllStringSubmatch(html, -1)
	if len(matches) == 0 {
		// Try alternative regex
		matches = altResultRegex.FindAllStringSubmatch(html, -1)
	}

	for i, match := range matches {
		if len(match) >= 4 && i < 20 { // Limit to 20 results
			rawURL := match[1]
			title := y.cleanHTML(match[2])
			description := y.cleanHTML(match[3])

			// Clean up Yahoo's redirect URLs
			finalURL := y.cleanYahooURL(rawURL)

			// Skip invalid URLs
			if finalURL == "" || strings.Contains(finalURL, "yahoo.com") {
				continue
			}

			result := models.SearchResult{
				ID:          y.generateID(finalURL),
				Title:       title,
				URL:         finalURL,
				Description: description,
				Source:      y.Name(),
				Rank:        i + 1,
				Score:       0.85 - float64(i)*0.03, // Yahoo results quality scoring
				Timestamp:   time.Now(),
				Favicon:     "https://www.yahoo.com/favicon.ico",
			}

			results = append(results, result)
		}
	}

	// If we didn't get results with regex, try a simpler approach
	if len(results) == 0 {
		results = y.parseYahooResultsSimple(html, request)
	}

	return results
}

// parseYahooResultsSimple is a fallback parser for Yahoo results
func (y *YahooProvider) parseYahooResultsSimple(html string, request models.SearchRequest) []models.SearchResult {
	var results []models.SearchResult

	// Look for basic link patterns in Yahoo
	linkRegex := regexp.MustCompile(`<a[^>]*href="(https?://[^"]*)"[^>]*>([^<]+)</a>`)
	matches := linkRegex.FindAllStringSubmatch(html, -1)

	for i, match := range matches {
		if len(match) >= 3 && i < 15 {
			url := match[1]
			title := y.cleanHTML(match[2])

			// Skip Yahoo internal URLs
			if strings.Contains(url, "yahoo.com") || strings.Contains(url, "search.yahoo.com") {
				continue
			}

			result := models.SearchResult{
				ID:          y.generateID(url),
				Title:       title,
				URL:         url,
				Description: fmt.Sprintf("Search result for %s from Yahoo", request.Query),
				Source:      y.Name(),
				Rank:        i + 1,
				Score:       0.75 - float64(i)*0.04,
				Timestamp:   time.Now(),
				Favicon:     "https://www.yahoo.com/favicon.ico",
			}

			results = append(results, result)
		}
	}

	return results
}

// cleanYahooURL cleans Yahoo's redirect URLs
func (y *YahooProvider) cleanYahooURL(rawURL string) string {
	// Yahoo sometimes uses redirect URLs like /r/some-encoded-url
	if strings.HasPrefix(rawURL, "/r/") {
		// Try to decode the URL
		if decoded, err := url.QueryUnescape(rawURL[3:]); err == nil {
			return decoded
		}
	}

	// Handle RU parameter in Yahoo URLs
	if strings.Contains(rawURL, "RU=") {
		if u, err := url.Parse(rawURL); err == nil {
			if ru := u.Query().Get("RU"); ru != "" {
				if decoded, err := url.QueryUnescape(ru); err == nil {
					return decoded
				}
			}
		}
	}

	return rawURL
}

// cleanHTML removes HTML tags and decodes entities
func (y *YahooProvider) cleanHTML(text string) string {
	// Remove HTML tags
	re := regexp.MustCompile(`<[^>]*>`)
	text = re.ReplaceAllString(text, "")

	// Decode common HTML entities
	text = strings.ReplaceAll(text, "&amp;", "&")
	text = strings.ReplaceAll(text, "&lt;", "<")
	text = strings.ReplaceAll(text, "&gt;", ">")
	text = strings.ReplaceAll(text, "&quot;", "\"")
	text = strings.ReplaceAll(text, "&#39;", "'")
	text = strings.ReplaceAll(text, "&nbsp;", " ")

	// Clean up whitespace
	text = regexp.MustCompile(`\s+`).ReplaceAllString(text, " ")
	return strings.TrimSpace(text)
}

// generateID generates a unique ID for a search result
func (y *YahooProvider) generateID(url string) string {
	hash := md5.Sum([]byte(url))
	return fmt.Sprintf("yahoo_%x", hash)
}
