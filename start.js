#!/usr/bin/env node

const { spawn } = require('child_process');
const path = require('path');

console.log('🚀 Starting Ultra Search Development Environment...');
console.log('=' .repeat(60));

// Kill any existing processes on the ports
const killPort = (port) => {
  return new Promise((resolve) => {
    const kill = spawn('powershell', ['-Command', `Get-Process -Id (Get-NetTCPConnection -LocalPort ${port} -ErrorAction SilentlyContinue).OwningProcess -ErrorAction SilentlyContinue | Stop-Process -Force -ErrorAction SilentlyContinue`]);
    kill.on('close', () => resolve());
  });
};

async function startServices() {
  // Kill existing processes
  console.log('🧹 Cleaning up existing processes...');
  await killPort(8080);
  await killPort(3000);
  
  console.log('✅ Ports cleaned up!');
  
  // Start backend
  console.log('🔥 Starting Go backend...');
  const backend = spawn('go', ['run', 'cmd/server/main.go'], {
    cwd: process.cwd(),
    stdio: ['inherit', 'pipe', 'pipe']
  });

  backend.stdout.on('data', (data) => {
    console.log(`[BACKEND] ${data.toString().trim()}`);
  });

  backend.stderr.on('data', (data) => {
    console.log(`[BACKEND] ${data.toString().trim()}`);
  });

  // Wait a bit for backend to start
  await new Promise(resolve => setTimeout(resolve, 2000));

  // Start frontend
  console.log('⚡ Starting Astro frontend...');
  const frontend = spawn('bun', ['run', 'dev'], {
    cwd: path.join(process.cwd(), 'frontend'),
    stdio: ['inherit', 'pipe', 'pipe']
  });

  frontend.stdout.on('data', (data) => {
    console.log(`[FRONTEND] ${data.toString().trim()}`);
  });

  frontend.stderr.on('data', (data) => {
    console.log(`[FRONTEND] ${data.toString().trim()}`);
  });

  console.log('');
  console.log('🎉 Development servers started!');
  console.log('🔗 Backend API: http://localhost:8080');
  console.log('🌐 Frontend: http://localhost:3000');
  console.log('');
  console.log('⏹️  Press Ctrl+C to stop all services');
  console.log('=' .repeat(60));

  // Handle cleanup
  process.on('SIGINT', () => {
    console.log('\n🛑 Stopping development servers...');
    backend.kill('SIGTERM');
    frontend.kill('SIGTERM');
    process.exit(0);
  });

  process.on('SIGTERM', () => {
    backend.kill('SIGTERM');
    frontend.kill('SIGTERM');
    process.exit(0);
  });
}

startServices().catch(console.error);
