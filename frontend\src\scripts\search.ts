// Main search functionality - optimized and modular
import type { SearchState } from './types.ts';
import { SearchAPI } from './api.ts';
import { elements } from './dom.ts';
import { UIManager } from './ui.ts';

export class SearchManager {
  private state: SearchState = {
    currentQuery: '',
    currentPage: 1,
    isLoading: false
  };

  private autocompleteTimeout: number | undefined;

  constructor() {
    this.initializeEventListeners();
    this.handleURLParams();
  }

  private initializeEventListeners(): void {
    // Search form submission
    elements.searchForm?.addEventListener('submit', this.handleSearch.bind(this));

    // Quick search buttons
    elements.quickSearchButtons.forEach(button => {
      button.addEventListener('click', this.handleQuickSearch.bind(this));
    });

    // Autocomplete input
    elements.searchInput?.addEventListener('input', this.handleAutocompleteInput.bind(this));

    // Hide autocomplete when clicking outside
    document.addEventListener('click', this.handleDocumentClick.bind(this));
  }

  private async handleSearch(e: Event): Promise<void> {
    e.preventDefault();

    const query = elements.searchInput?.value?.trim();
    if (!query || this.state.isLoading) return;

    this.state.currentQuery = query;
    this.state.currentPage = 1;

    await this.performSearch(query, 1);
  }

  private handleQuickSearch(e: Event): void {
    const button = e.target as HTMLButtonElement;
    const query = button.getAttribute('data-query');

    if (query && elements.searchInput) {
      elements.searchInput.value = query;
      this.state.currentQuery = query;
      this.state.currentPage = 1;
      this.performSearch(query, 1);
    }
  }

  private handleAutocompleteInput(e: Event): void {
    const target = e.target as HTMLInputElement;
    const query = target.value.trim();

    if (this.autocompleteTimeout) {
      clearTimeout(this.autocompleteTimeout);
    }

    if (query.length >= 2) {
      this.autocompleteTimeout = setTimeout(() => {
        this.fetchAutocomplete(query);
      }, 300);
    } else {
      this.hideAutocomplete();
    }
  }

  private handleDocumentClick(e: Event): void {
    const target = e.target as Node;
    if (elements.searchForm && !elements.searchForm.contains(target)) {
      this.hideAutocomplete();
    }
  }

  public async performSearch(query: string, page: number = 1): Promise<void> {
    if (this.state.isLoading) return;

    this.state.isLoading = true;
    UIManager.showLoading();
    this.hideAutocomplete();

    try {
      const data = await SearchAPI.search(query, page);

      UIManager.displayResults(data);
      UIManager.updateSearchInfo(data);
      UIManager.updatePagination(data, this.goToPage.bind(this));

      this.state.currentPage = page;

      // Update URL without page reload
      this.updateURL(query, page);

    } catch (error) {
      console.error('Search error:', error);
      UIManager.displayError('Search failed. Please try again.');
    } finally {
      this.state.isLoading = false;
      UIManager.hideLoading();
    }
  }

  private async fetchAutocomplete(query: string): Promise<void> {
    try {
      const data = await SearchAPI.autocomplete(query);
      this.displayAutocomplete(data.suggestions);
    } catch (error) {
      console.error('Autocomplete error:', error);
    }
  }

  private displayAutocomplete(suggestions: string[]): void {
    if (!suggestions || suggestions.length === 0 || !elements.autocomplete) {
      this.hideAutocomplete();
      return;
    }

    elements.autocomplete.innerHTML = suggestions.map(suggestion =>
      `<div class="px-4 py-2 hover:bg-gray-100 cursor-pointer autocomplete-item" data-suggestion="${suggestion}">
        ${suggestion}
      </div>`
    ).join('');

    // Add click handlers to suggestions
    elements.autocomplete.querySelectorAll('.autocomplete-item').forEach(item => {
      item.addEventListener('click', () => {
        const suggestionText = item.getAttribute('data-suggestion');
        if (suggestionText && elements.searchInput && elements.searchForm) {
          elements.searchInput.value = suggestionText;
          this.hideAutocomplete();
          elements.searchForm.dispatchEvent(new Event('submit'));
        }
      });
    });

    elements.autocomplete.classList.remove('hidden');
  }

  private hideAutocomplete(): void {
    elements.autocomplete?.classList.add('hidden');
  }

  public async goToPage(page: number): Promise<void> {
    if (this.state.currentQuery) {
      await this.performSearch(this.state.currentQuery, page);
      window.scrollTo({ top: 0, behavior: 'smooth' });
    }
  }

  private updateURL(query: string, page: number): void {
    const url = new URL(window.location.href);
    url.searchParams.set('q', query);
    if (page > 1) {
      url.searchParams.set('page', page.toString());
    } else {
      url.searchParams.delete('page');
    }
    window.history.replaceState({}, '', url.toString());
  }

  private handleURLParams(): void {
    const urlParams = new URLSearchParams(window.location.search);
    const query = urlParams.get('q');
    const pageParam = urlParams.get('page');
    const page = pageParam ? parseInt(pageParam) : 1;

    if (query && elements.searchInput) {
      elements.searchInput.value = query;
      this.state.currentQuery = query;
      this.state.currentPage = page;
      this.performSearch(query, page);
    }
  }
}
