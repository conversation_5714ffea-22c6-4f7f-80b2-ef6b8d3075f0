package metrics

import (
	"sync"
	"time"
)

// PerformanceMetrics tracks search performance metrics
type PerformanceMetrics struct {
	mu                  sync.RWMutex
	TotalSearches       int64                     `json:"total_searches"`
	AverageResponseTime time.Duration             `json:"average_response_time"`
	ProviderPerformance map[string]*ProviderStats `json:"provider_performance"`
	RecentSearches      []SearchMetric            `json:"recent_searches"`
	ErrorCount          int64                     `json:"error_count"`
	CacheHitRate        float64                   `json:"cache_hit_rate"`
	TotalCacheHits      int64                     `json:"total_cache_hits"`
	TotalCacheMisses    int64                     `json:"total_cache_misses"`
	StartTime           time.Time                 `json:"start_time"`
}

// ProviderStats tracks performance for individual providers
type ProviderStats struct {
	TotalRequests      int64         `json:"total_requests"`
	SuccessfulRequests int64         `json:"successful_requests"`
	FailedRequests     int64         `json:"failed_requests"`
	AverageLatency     time.Duration `json:"average_latency"`
	TotalResults       int64         `json:"total_results"`
	LastUsed           time.Time     `json:"last_used"`
	SuccessRate        float64       `json:"success_rate"`
}

// SearchMetric represents a single search operation metric
type SearchMetric struct {
	Query        string        `json:"query"`
	ResponseTime time.Duration `json:"response_time"`
	ResultCount  int           `json:"result_count"`
	Providers    []string      `json:"providers"`
	Timestamp    time.Time     `json:"timestamp"`
	CacheHit     bool          `json:"cache_hit"`
	Error        string        `json:"error,omitempty"`
}

// NewPerformanceMetrics creates a new performance metrics tracker
func NewPerformanceMetrics() *PerformanceMetrics {
	return &PerformanceMetrics{
		ProviderPerformance: make(map[string]*ProviderStats),
		RecentSearches:      make([]SearchMetric, 0, 100), // Keep last 100 searches
		StartTime:           time.Now(),
	}
}

// RecordSearch records a search operation
func (pm *PerformanceMetrics) RecordSearch(metric SearchMetric) {
	pm.mu.Lock()
	defer pm.mu.Unlock()

	pm.TotalSearches++

	// Update average response time
	if pm.TotalSearches == 1 {
		pm.AverageResponseTime = metric.ResponseTime
	} else {
		// Calculate running average
		total := time.Duration(pm.TotalSearches-1) * pm.AverageResponseTime
		pm.AverageResponseTime = (total + metric.ResponseTime) / time.Duration(pm.TotalSearches)
	}

	// Record error if present
	if metric.Error != "" {
		pm.ErrorCount++
	}

	// Update cache metrics
	if metric.CacheHit {
		pm.TotalCacheHits++
	} else {
		pm.TotalCacheMisses++
	}

	total := pm.TotalCacheHits + pm.TotalCacheMisses
	if total > 0 {
		pm.CacheHitRate = float64(pm.TotalCacheHits) / float64(total)
	}

	// Add to recent searches (keep only last 100)
	pm.RecentSearches = append(pm.RecentSearches, metric)
	if len(pm.RecentSearches) > 100 {
		pm.RecentSearches = pm.RecentSearches[1:]
	}
}

// RecordProviderResult records a result from a specific provider
func (pm *PerformanceMetrics) RecordProviderResult(provider string, latency time.Duration, resultCount int, success bool) {
	pm.mu.Lock()
	defer pm.mu.Unlock()

	if pm.ProviderPerformance[provider] == nil {
		pm.ProviderPerformance[provider] = &ProviderStats{}
	}

	stats := pm.ProviderPerformance[provider]
	stats.TotalRequests++
	stats.LastUsed = time.Now()
	stats.TotalResults += int64(resultCount)

	if success {
		stats.SuccessfulRequests++
	} else {
		stats.FailedRequests++
	}

	// Update average latency
	if stats.TotalRequests == 1 {
		stats.AverageLatency = latency
	} else {
		// Calculate running average
		total := time.Duration(stats.TotalRequests-1) * stats.AverageLatency
		stats.AverageLatency = (total + latency) / time.Duration(stats.TotalRequests)
	}

	// Calculate success rate
	if stats.TotalRequests > 0 {
		stats.SuccessRate = float64(stats.SuccessfulRequests) / float64(stats.TotalRequests)
	}
}

// GetStats returns current performance statistics
func (pm *PerformanceMetrics) GetStats() map[string]interface{} {
	pm.mu.RLock()
	defer pm.mu.RUnlock()

	uptime := time.Since(pm.StartTime)

	stats := map[string]interface{}{
		"uptime":                uptime.String(),
		"total_searches":        pm.TotalSearches,
		"average_response_time": pm.AverageResponseTime.String(),
		"error_count":           pm.ErrorCount,
		"cache_hit_rate":        pm.CacheHitRate,
		"total_cache_hits":      pm.TotalCacheHits,
		"total_cache_misses":    pm.TotalCacheMisses,
		"provider_performance":  pm.ProviderPerformance,
		"searches_per_minute":   pm.calculateSearchesPerMinute(),
		"error_rate":            pm.calculateErrorRate(),
	}

	return stats
}

// GetRecentSearches returns recent search metrics
func (pm *PerformanceMetrics) GetRecentSearches(limit int) []SearchMetric {
	pm.mu.RLock()
	defer pm.mu.RUnlock()

	if limit <= 0 || limit > len(pm.RecentSearches) {
		limit = len(pm.RecentSearches)
	}

	// Return the most recent searches
	start := len(pm.RecentSearches) - limit
	if start < 0 {
		start = 0
	}

	return pm.RecentSearches[start:]
}

// calculateSearchesPerMinute calculates the current searches per minute rate
func (pm *PerformanceMetrics) calculateSearchesPerMinute() float64 {
	uptime := time.Since(pm.StartTime)
	if uptime.Minutes() == 0 {
		return 0
	}
	return float64(pm.TotalSearches) / uptime.Minutes()
}

// calculateErrorRate calculates the current error rate
func (pm *PerformanceMetrics) calculateErrorRate() float64 {
	if pm.TotalSearches == 0 {
		return 0
	}
	return float64(pm.ErrorCount) / float64(pm.TotalSearches)
}

// GetProviderRanking returns providers ranked by performance
func (pm *PerformanceMetrics) GetProviderRanking() []ProviderRanking {
	pm.mu.RLock()
	defer pm.mu.RUnlock()

	var rankings []ProviderRanking

	for name, stats := range pm.ProviderPerformance {
		score := pm.calculateProviderScore(stats)
		rankings = append(rankings, ProviderRanking{
			Name:  name,
			Score: score,
			Stats: *stats,
		})
	}

	// Sort by score (higher is better)
	for i := 0; i < len(rankings)-1; i++ {
		for j := i + 1; j < len(rankings); j++ {
			if rankings[i].Score < rankings[j].Score {
				rankings[i], rankings[j] = rankings[j], rankings[i]
			}
		}
	}

	return rankings
}

// ProviderRanking represents a provider's performance ranking
type ProviderRanking struct {
	Name  string        `json:"name"`
	Score float64       `json:"score"`
	Stats ProviderStats `json:"stats"`
}

// calculateProviderScore calculates a performance score for a provider
func (pm *PerformanceMetrics) calculateProviderScore(stats *ProviderStats) float64 {
	if stats.TotalRequests == 0 {
		return 0
	}

	// Base score from success rate (0-40 points)
	successScore := stats.SuccessRate * 40

	// Latency score (0-30 points, lower latency = higher score)
	latencyMs := float64(stats.AverageLatency.Milliseconds())
	latencyScore := 30.0
	if latencyMs > 1000 {
		latencyScore = 30.0 * (2000.0 - latencyMs) / 1000.0
		if latencyScore < 0 {
			latencyScore = 0
		}
	}

	// Results per request score (0-20 points)
	avgResults := float64(stats.TotalResults) / float64(stats.TotalRequests)
	resultsScore := avgResults * 2 // Assuming 10 results per request is good
	if resultsScore > 20 {
		resultsScore = 20
	}

	// Reliability score based on recent usage (0-10 points)
	reliabilityScore := 10.0
	if time.Since(stats.LastUsed) > time.Hour {
		reliabilityScore = 5.0
	}

	return successScore + latencyScore + resultsScore + reliabilityScore
}

// Reset resets all metrics
func (pm *PerformanceMetrics) Reset() {
	pm.mu.Lock()
	defer pm.mu.Unlock()

	pm.TotalSearches = 0
	pm.AverageResponseTime = 0
	pm.ProviderPerformance = make(map[string]*ProviderStats)
	pm.RecentSearches = make([]SearchMetric, 0, 100)
	pm.ErrorCount = 0
	pm.CacheHitRate = 0
	pm.TotalCacheHits = 0
	pm.TotalCacheMisses = 0
	pm.StartTime = time.Now()
}
