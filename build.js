#!/usr/bin/env bun

import { spawn } from 'child_process';
import { promisify } from 'util';
import { exec as execCallback } from 'child_process';
import fs from 'fs';
import path from 'path';
const exec = promisify(execCallback);

async function buildProduction() {
  console.log('🏗️  Building Ultra Search for Production');
  console.log('=======================================');

  try {
    // Build frontend
    console.log('🎨 Building frontend...');
    const frontendBuild = spawn('bun', ['run', 'build'], {
      cwd: 'frontend',
      stdio: 'inherit'
    });

    await new Promise((resolve, reject) => {
      frontendBuild.on('close', (code) => {
        if (code === 0) {
          console.log('✅ Frontend built successfully!');
          resolve();
        } else {
          reject(new Error(`Frontend build failed with code ${code}`));
        }
      });
    });

    // Build backend
    console.log('⚡ Building backend...');
    await exec('go build -o ultra_search.exe ./cmd/server');
    console.log('✅ Backend built successfully!');

    // Check if dist directory exists
    const distPath = path.join('frontend', 'dist');
    if (fs.existsSync(distPath)) {
      const files = fs.readdirSync(distPath);
      console.log(`📦 Frontend assets: ${files.length} files`);
    }

    console.log('\n🎉 Build completed successfully!');
    console.log('🚀 Run: ./ultra_search.exe');
    console.log('🔧 Dev mode: ./ultra_search.exe --dev');
    console.log('📱 Development: node dev.js');

  } catch (error) {
    console.error('❌ Build failed:', error.message);
    process.exit(1);
  }
}

buildProduction();
