#!/usr/bin/env node

const { spawn } = require('child_process');
const { promisify } = require('util');
const exec = promisify(require('child_process').exec);

// Kill processes on specific ports
async function killPort(port) {
  try {
    if (process.platform === 'win32') {
      await exec(`netstat -ano | findstr :${port}`).then(async (result) => {
        const lines = result.stdout.split('\n');
        for (const line of lines) {
          const parts = line.trim().split(/\s+/);
          if (parts.length > 4 && parts[1].includes(`:${port}`)) {
            const pid = parts[parts.length - 1];
            if (pid && pid !== '0') {
              try {
                await exec(`taskkill /F /PID ${pid}`);
                console.log(`✅ Killed process on port ${port} (PID: ${pid})`);
              } catch (e) {
                // Process might already be dead
              }
            }
          }
        }
      });
    } else {
      await exec(`lsof -ti:${port} | xargs kill -9`);
    }
  } catch (error) {
    // Port might not be in use
  }
}

async function startDevelopment() {
  console.log('🚀 Starting Ultra Search Development Mode');
  console.log('=====================================');
  
  // Kill existing processes
  console.log('🧹 Cleaning up existing processes...');
  await killPort(8080);
  await killPort(3000);
  
  console.log('✅ Ports cleaned up!');
  
  // Start Astro dev server
  console.log('🎨 Starting Astro frontend dev server...');
  const astro = spawn('bun', ['run', 'dev'], {
    cwd: 'frontend',
    stdio: ['inherit', 'pipe', 'pipe']
  });

  astro.stdout.on('data', (data) => {
    console.log(`[FRONTEND] ${data.toString().trim()}`);
  });

  astro.stderr.on('data', (data) => {
    console.log(`[FRONTEND] ${data.toString().trim()}`);
  });

  // Wait a bit for Astro to start
  await new Promise(resolve => setTimeout(resolve, 3000));
  
  // Start Go backend in dev mode
  console.log('⚡ Starting Go backend in dev mode...');
  const backend = spawn('./ultra_search.exe', ['--dev'], {
    cwd: process.cwd(),
    stdio: ['inherit', 'pipe', 'pipe']
  });

  backend.stdout.on('data', (data) => {
    console.log(`[BACKEND] ${data.toString().trim()}`);
  });

  backend.stderr.on('data', (data) => {
    console.log(`[BACKEND] ${data.toString().trim()}`);
  });

  // Handle process termination
  process.on('SIGINT', () => {
    console.log('\n🛑 Shutting down development servers...');
    astro.kill();
    backend.kill();
    process.exit(0);
  });

  console.log('\n🎉 Development servers started!');
  console.log('📱 Frontend (Astro): http://localhost:3000');
  console.log('🔧 Backend (Go): http://localhost:8080');
  console.log('🌐 Full App: http://localhost:8080 (proxies to frontend)');
  console.log('\nPress Ctrl+C to stop all servers');
}

startDevelopment().catch(console.error);
