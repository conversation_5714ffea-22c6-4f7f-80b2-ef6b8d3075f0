package models

import (
	"time"
)

// SearchRequest represents an incoming search query
type SearchRequest struct {
	Query    string `json:"query" binding:"required"`
	Page     int    `json:"page,omitempty"`
	PerPage  int    `json:"per_page,omitempty"`
	Language string `json:"language,omitempty"`
	Region   string `json:"region,omitempty"`
	SafeSearch bool `json:"safe_search,omitempty"`
}

// SearchResult represents a single search result
type SearchResult struct {
	ID          string    `json:"id"`
	Title       string    `json:"title"`
	URL         string    `json:"url"`
	Description string    `json:"description"`
	Source      string    `json:"source"`
	Rank        int       `json:"rank"`
	Score       float64   `json:"score"`
	Timestamp   time.Time `json:"timestamp"`
	Favicon     string    `json:"favicon,omitempty"`
	Thumbnail   string    `json:"thumbnail,omitempty"`
}

// SearchResponse represents the aggregated search response
type SearchResponse struct {
	Query       string         `json:"query"`
	Results     []SearchResult `json:"results"`
	TotalCount  int            `json:"total_count"`
	Page        int            `json:"page"`
	PerPage     int            `json:"per_page"`
	Sources     []string       `json:"sources"`
	SearchTime  time.Duration  `json:"search_time"`
	Suggestions []string       `json:"suggestions,omitempty"`
}

// SearchProvider represents a search engine provider
type SearchProvider interface {
	Name() string
	Search(request SearchRequest) ([]SearchResult, error)
	IsAvailable() bool
	GetRateLimit() time.Duration
}

// ProviderResult represents results from a specific provider
type ProviderResult struct {
	Provider string
	Results  []SearchResult
	Error    error
	Duration time.Duration
}

// SearchMetrics represents search analytics data
type SearchMetrics struct {
	Query        string    `json:"query"`
	ResultCount  int       `json:"result_count"`
	SearchTime   time.Duration `json:"search_time"`
	ProvidersUsed []string  `json:"providers_used"`
	Timestamp    time.Time `json:"timestamp"`
	UserAgent    string    `json:"user_agent,omitempty"`
	IP           string    `json:"ip,omitempty"`
}

// ErrorResponse represents an API error response
type ErrorResponse struct {
	Error   string `json:"error"`
	Code    int    `json:"code"`
	Message string `json:"message"`
}

// AutocompleteRequest represents an autocomplete query
type AutocompleteRequest struct {
	Query string `json:"query" binding:"required"`
	Limit int    `json:"limit,omitempty"`
}

// AutocompleteResponse represents autocomplete suggestions
type AutocompleteResponse struct {
	Query       string   `json:"query"`
	Suggestions []string `json:"suggestions"`
}
