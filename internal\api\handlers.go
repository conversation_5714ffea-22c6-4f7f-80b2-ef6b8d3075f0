package api

import (
	"context"
	"net/http"
	"strconv"
	"strings"
	"time"

	"ultra_search/internal/aggregator"
	"ultra_search/internal/cache"
	"ultra_search/internal/config"
	"ultra_search/internal/metrics"
	"ultra_search/internal/models"

	"github.com/gin-gonic/gin"
)

// SearchHandler handles search requests
type SearchHandler struct {
	aggregator   *aggregator.SearchAggregator
	cacheManager *cache.CacheManager
	config       *config.Config
	metrics      *metrics.PerformanceMetrics
}

// NewSearchHandler creates a new search handler
func NewSearchHandler(agg *aggregator.SearchAggregator, cm *cache.CacheManager, cfg *config.Config, m *metrics.PerformanceMetrics) *SearchHandler {
	return &SearchHandler{
		aggregator:   agg,
		cacheManager: cm,
		config:       cfg,
		metrics:      m,
	}
}

// Search handles search requests
func (h *SearchHandler) Search(c *gin.Context) {
	var request models.SearchRequest

	// Parse query parameters
	request.Query = strings.TrimSpace(c.Query("q"))
	if request.Query == "" {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "missing_query",
			Code:    http.StatusBadRequest,
			Message: "Query parameter 'q' is required",
		})
		return
	}

	// Parse optional parameters
	if page := c.Query("page"); page != "" {
		if p, err := strconv.Atoi(page); err == nil && p > 0 {
			request.Page = p
		}
	}
	if request.Page == 0 {
		request.Page = 1
	}

	if perPage := c.Query("per_page"); perPage != "" {
		if pp, err := strconv.Atoi(perPage); err == nil && pp > 0 {
			request.PerPage = pp
		}
	}
	if request.PerPage == 0 {
		request.PerPage = h.config.Search.DefaultResultsPerPage
	}
	if request.PerPage > h.config.Search.MaxResultsPerPage {
		request.PerPage = h.config.Search.MaxResultsPerPage
	}

	request.Language = c.Query("lang")
	request.Region = c.Query("region")
	request.SafeSearch = c.Query("safe") == "true"

	// Check cache first
	if cached, found := h.cacheManager.SearchCache.Get(request); found {
		c.Header("X-Cache", "HIT")
		c.JSON(http.StatusOK, cached)
		return
	}

	// Perform search
	ctx, cancel := context.WithTimeout(context.Background(), h.config.Search.SearchTimeout)
	defer cancel()

	response, err := h.aggregator.Search(ctx, request)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Error:   "search_failed",
			Code:    http.StatusInternalServerError,
			Message: err.Error(),
		})
		return
	}

	// Cache the response
	h.cacheManager.SearchCache.Set(request, response, h.config.Cache.TTL)

	c.Header("X-Cache", "MISS")
	c.JSON(http.StatusOK, response)
}

// Autocomplete handles autocomplete requests
func (h *SearchHandler) Autocomplete(c *gin.Context) {
	query := strings.TrimSpace(c.Query("q"))
	if query == "" {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "missing_query",
			Code:    http.StatusBadRequest,
			Message: "Query parameter 'q' is required",
		})
		return
	}

	limit := 5
	if l := c.Query("limit"); l != "" {
		if parsed, err := strconv.Atoi(l); err == nil && parsed > 0 && parsed <= 10 {
			limit = parsed
		}
	}

	// Check cache first
	if cached, found := h.cacheManager.AutocompleteCache.Get(query); found {
		suggestions := cached
		if len(suggestions) > limit {
			suggestions = suggestions[:limit]
		}

		c.Header("X-Cache", "HIT")
		c.JSON(http.StatusOK, models.AutocompleteResponse{
			Query:       query,
			Suggestions: suggestions,
		})
		return
	}

	// Generate simple autocomplete suggestions
	suggestions := h.generateAutocompleteSuggestions(query, limit)

	// Cache the suggestions
	h.cacheManager.AutocompleteCache.Set(query, suggestions, 1*time.Hour)

	c.Header("X-Cache", "MISS")
	c.JSON(http.StatusOK, models.AutocompleteResponse{
		Query:       query,
		Suggestions: suggestions,
	})
}

// generateAutocompleteSuggestions generates simple autocomplete suggestions
func (h *SearchHandler) generateAutocompleteSuggestions(query string, limit int) []string {
	// This is a simple implementation. In production, you might want to:
	// 1. Use a dedicated autocomplete service
	// 2. Maintain a database of popular queries
	// 3. Use machine learning for better suggestions

	commonSuffixes := []string{
		"tutorial",
		"guide",
		"how to",
		"best",
		"free",
		"online",
		"download",
		"review",
		"comparison",
		"tips",
	}

	var suggestions []string
	for _, suffix := range commonSuffixes {
		if len(suggestions) >= limit {
			break
		}
		suggestion := query + " " + suffix
		suggestions = append(suggestions, suggestion)
	}

	return suggestions
}

// Health handles health check requests
func (h *SearchHandler) Health(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"status":    "healthy",
		"timestamp": time.Now().UTC(),
		"version":   "1.0.0",
		"message":   "Ultra Search API - Hot reload working perfectly! 🔥",
	})
}

// Stats handles statistics requests
func (h *SearchHandler) Stats(c *gin.Context) {
	stats := map[string]interface{}{
		"cache":       h.cacheManager.GetOverallStats(),
		"performance": h.metrics.GetStats(),
		"providers":   h.metrics.GetProviderRanking(),
		"timestamp":   time.Now().UTC(),
	}

	c.JSON(http.StatusOK, stats)
}

// ClearCache handles cache clearing requests
func (h *SearchHandler) ClearCache(c *gin.Context) {
	cacheType := c.Query("type")

	switch cacheType {
	case "search":
		h.cacheManager.SearchCache.Clear()
	case "autocomplete":
		h.cacheManager.AutocompleteCache.Cache.Flush()
	case "all", "":
		h.cacheManager.ClearAll()
	default:
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "invalid_cache_type",
			Code:    http.StatusBadRequest,
			Message: "Valid cache types are: search, autocomplete, all",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Cache cleared successfully",
		"type":    cacheType,
	})
}
