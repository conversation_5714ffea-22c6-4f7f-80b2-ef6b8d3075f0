package search

import (
	"crypto/md5"
	"fmt"
	"net/url"
	"regexp"
	"strings"
	"time"

	"ultra_search/internal/models"

	"github.com/go-resty/resty/v2"
)

// BraveProvider implements SearchProvider for Brave Search
type BraveProvider struct {
	client    *resty.Client
	baseURL   string
	rateLimit time.Duration
}

// NewBraveProvider creates a new Brave search provider
func NewBraveProvider() *BraveProvider {
	client := resty.New()
	client.SetTimeout(10 * time.Second)
	client.SetHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
	client.SetHeader("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8")
	client.SetHeader("Accept-Language", "en-US,en;q=0.9")
	client.SetHeader("Accept-Encoding", "gzip, deflate, br")
	client.SetHeader("DNT", "1")
	client.SetHeader("Connection", "keep-alive")
	client.SetHeader("Upgrade-Insecure-Requests", "1")
	client.SetHeader("Sec-Fetch-Dest", "document")
	client.SetHeader("Sec-Fetch-Mode", "navigate")
	client.SetHeader("Sec-Fetch-Site", "none")
	client.SetHeader("Sec-Fetch-User", "?1")

	return &BraveProvider{
		client:    client,
		baseURL:   "https://search.brave.com/search",
		rateLimit: 1800 * time.Millisecond, // Conservative rate limit
	}
}

// Name returns the provider name
func (b *BraveProvider) Name() string {
	return "brave"
}

// IsAvailable checks if the provider is available
func (b *BraveProvider) IsAvailable() bool {
	return true
}

// GetRateLimit returns the rate limit for this provider
func (b *BraveProvider) GetRateLimit() time.Duration {
	return b.rateLimit
}

// Search performs a search using Brave web scraping
func (b *BraveProvider) Search(request models.SearchRequest) ([]models.SearchResult, error) {
	// Build search URL
	params := url.Values{}
	params.Set("q", request.Query)
	params.Set("source", "web")
	if request.Language != "" {
		params.Set("country", request.Language)
	}
	if request.SafeSearch {
		params.Set("safesearch", "strict")
	}

	searchURL := b.baseURL + "?" + params.Encode()

	// Make the request
	resp, err := b.client.R().Get(searchURL)
	if err != nil {
		return nil, fmt.Errorf("brave search request failed: %w", err)
	}

	if resp.StatusCode() != 200 {
		return nil, fmt.Errorf("brave search returned status %d", resp.StatusCode())
	}

	// Parse results
	results := b.parseBraveResults(string(resp.Body()), request)
	return results, nil
}

// parseBraveResults parses Brave search results from HTML
func (b *BraveProvider) parseBraveResults(html string, request models.SearchRequest) []models.SearchResult {
	var results []models.SearchResult

	// Brave uses specific class names for search results
	// Primary regex for Brave organic results
	resultRegex := regexp.MustCompile(`<div[^>]*class="[^"]*result[^"]*"[^>]*>.*?<h3[^>]*class="[^"]*heading[^"]*"[^>]*>.*?<a[^>]*href="([^"]*)"[^>]*>(.*?)</a>.*?</h3>.*?<p[^>]*class="[^"]*snippet[^"]*"[^>]*>(.*?)</p>`)

	// Alternative regex for different Brave layouts
	altResultRegex := regexp.MustCompile(`<div[^>]*class="[^"]*fdb[^"]*"[^>]*>.*?<h3[^>]*>.*?<a[^>]*href="([^"]*)"[^>]*>(.*?)</a>.*?</h3>.*?<div[^>]*class="[^"]*snippet[^"]*"[^>]*>(.*?)</div>`)

	// Simplified regex for basic results
	simpleRegex := regexp.MustCompile(`<a[^>]*class="[^"]*result-header[^"]*"[^>]*href="([^"]*)"[^>]*>(.*?)</a>`)

	// Try primary regex first
	matches := resultRegex.FindAllStringSubmatch(html, -1)
	if len(matches) == 0 {
		// Try alternative regex
		matches = altResultRegex.FindAllStringSubmatch(html, -1)
	}

	for i, match := range matches {
		if len(match) >= 4 && i < 20 { // Limit to 20 results
			rawURL := match[1]
			title := b.cleanHTML(match[2])
			description := b.cleanHTML(match[3])

			// Clean up Brave URLs
			finalURL := b.cleanBraveURL(rawURL)

			// Skip invalid URLs
			if finalURL == "" || strings.Contains(finalURL, "search.brave.com") {
				continue
			}

			result := models.SearchResult{
				ID:          b.generateID(finalURL),
				Title:       title,
				URL:         finalURL,
				Description: description,
				Source:      b.Name(),
				Rank:        i + 1,
				Score:       0.87 - float64(i)*0.03, // Brave has good independent results
				Timestamp:   time.Now(),
				Favicon:     "https://search.brave.com/favicon.ico",
			}

			results = append(results, result)
		}
	}

	// If we didn't get results with complex regex, try simple approach
	if len(results) == 0 {
		matches = simpleRegex.FindAllStringSubmatch(html, -1)
		for i, match := range matches {
			if len(match) >= 3 && i < 15 {
				rawURL := match[1]
				title := b.cleanHTML(match[2])

				finalURL := b.cleanBraveURL(rawURL)
				if finalURL == "" || strings.Contains(finalURL, "search.brave.com") {
					continue
				}

				result := models.SearchResult{
					ID:          b.generateID(finalURL),
					Title:       title,
					URL:         finalURL,
					Description: fmt.Sprintf("Search result for %s from Brave Search", request.Query),
					Source:      b.Name(),
					Rank:        i + 1,
					Score:       0.82 - float64(i)*0.04,
					Timestamp:   time.Now(),
					Favicon:     "https://search.brave.com/favicon.ico",
				}

				results = append(results, result)
			}
		}
	}

	return results
}

// cleanBraveURL cleans Brave's URLs
func (b *BraveProvider) cleanBraveURL(rawURL string) string {
	// Handle relative URLs
	if strings.HasPrefix(rawURL, "/") && !strings.HasPrefix(rawURL, "//") {
		return "https://search.brave.com" + rawURL
	}

	// Brave might use redirect URLs
	if strings.Contains(rawURL, "brave.com") && strings.Contains(rawURL, "url=") {
		if u, err := url.Parse(rawURL); err == nil {
			if targetURL := u.Query().Get("url"); targetURL != "" {
				if decoded, err := url.QueryUnescape(targetURL); err == nil {
					return decoded
				}
			}
		}
	}

	return rawURL
}

// cleanHTML removes HTML tags and decodes entities
func (b *BraveProvider) cleanHTML(text string) string {
	// Remove HTML tags
	re := regexp.MustCompile(`<[^>]*>`)
	text = re.ReplaceAllString(text, "")

	// Decode common HTML entities
	text = strings.ReplaceAll(text, "&amp;", "&")
	text = strings.ReplaceAll(text, "&lt;", "<")
	text = strings.ReplaceAll(text, "&gt;", ">")
	text = strings.ReplaceAll(text, "&quot;", "\"")
	text = strings.ReplaceAll(text, "&#39;", "'")
	text = strings.ReplaceAll(text, "&nbsp;", " ")
	text = strings.ReplaceAll(text, "&#x27;", "'")
	text = strings.ReplaceAll(text, "&#x2F;", "/")
	text = strings.ReplaceAll(text, "&hellip;", "...")

	// Clean up whitespace
	text = regexp.MustCompile(`\s+`).ReplaceAllString(text, " ")
	return strings.TrimSpace(text)
}

// generateID generates a unique ID for a search result
func (b *BraveProvider) generateID(url string) string {
	hash := md5.Sum([]byte(url))
	return fmt.Sprintf("brave_%x", hash)
}
