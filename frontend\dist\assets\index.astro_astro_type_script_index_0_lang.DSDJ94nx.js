const v="http://localhost:8080/api/v1";class f{static async search(e,t=1){const a=await fetch(`${v}/search?q=${encodeURIComponent(e)}&page=${t}&per_page=10`);if(!a.ok)throw new Error(`Search failed: ${a.status}`);return a.json()}static async autocomplete(e){const t=await fetch(`${v}/autocomplete?q=${encodeURIComponent(e)}&limit=5`);if(!t.ok)throw new Error(`Autocomplete failed: ${t.status}`);return t.json()}}class c{static cache=new Map;static get(e){return this.cache.has(e)||this.cache.set(e,document.querySelector(e)),this.cache.get(e)}static getAll(e){return document.querySelectorAll(e)}static clear(){this.cache.clear()}}const s={get searchForm(){return c.get("#search-form")},get searchInput(){return c.get("#search-input")},get autocomplete(){return c.get("#autocomplete")},get loading(){return c.get("#loading")},get results(){return c.get("#search-results")},get searchInfo(){return c.get("#search-info")},get pagination(){return c.get("#pagination")},get quickSearchButtons(){return c.getAll(".quick-search")}};class l{static showLoading(){s.loading?.classList.remove("hidden"),s.results&&(s.results.innerHTML=""),s.searchInfo?.classList.add("hidden"),s.pagination?.classList.add("hidden"),window.loadingAnimation&&window.loadingAnimation.start()}static hideLoading(){s.loading?.classList.add("hidden"),window.loadingAnimation&&window.loadingAnimation.stop()}static displayError(e){s.results&&(s.results.innerHTML=`
        <div class="text-center py-16">
          <div class="w-16 h-16 mx-auto mb-4 bg-red-100 rounded-full flex items-center justify-center">
            <svg class="w-8 h-8 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
          </div>
          <p class="text-red-500 text-lg font-medium">${e}</p>
          <p class="text-slate-400 text-sm mt-1">Please try again</p>
        </div>
      `),s.searchInfo?.classList.add("hidden"),s.pagination?.classList.add("hidden")}static displayResults(e){if(s.results){if(!e.results||e.results.length===0){s.results.innerHTML=`
        <div class="text-center py-8">
          <p class="text-gray-600 dark:text-gray-400">No results found</p>
        </div>
      `;return}s.results.innerHTML=e.results.map((t,a)=>{const r=new URL(t.url).hostname,d=t.favicon||`https://www.google.com/s2/favicons?domain=${r}&sz=16`;return`
        <article class="card p-6 mb-6 hover-lift fade-in" style="animation-delay: ${a*.1}s">
          <!-- Result header -->
          <div class="flex items-center gap-2 mb-2">
            <img src="${d}" alt="${r}" class="w-4 h-4 rounded-sm" onerror="this.style.display='none'">
            <div class="text-sm font-medium text-green-700 dark:text-green-400">${r}</div>
            <div class="flex items-center gap-1 ml-auto">
              <div class="w-2 h-2 rounded-full bg-${l.getProviderColor(t.source)}"></div>
              <span class="text-xs text-gray-500 dark:text-gray-400">${t.source}</span>
            </div>
          </div>

          <!-- Result title -->
          <h3 class="text-xl font-semibold mb-2">
            <a href="${t.url}"
               target="_blank"
               rel="noopener noreferrer"
               class="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 transition-colors duration-200 line-clamp-2">
              ${t.title}
            </a>
          </h3>

          <!-- Result description -->
          <p class="text-gray-700 dark:text-gray-300 text-sm leading-relaxed mb-3 line-clamp-3">
            ${t.description}
          </p>

          <!-- Result metadata -->
          <div class="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
            <div class="flex items-center gap-3">
              <span class="flex items-center gap-1">
                <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                </svg>
                Score: ${t.score.toFixed(2)}
              </span>
              <span class="flex items-center gap-1">
                <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"></path>
                </svg>
                Rank: #${t.rank}
              </span>
            </div>

            <!-- Quick actions -->
            <div class="flex items-center gap-2">
              <button class="p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded transition-colors duration-200"
                      onclick="UIManager.shareResult('${t.title.replace(/'/g,"\\'")}', '${t.url}')"
                      title="Share">
                <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"></path>
                </svg>
              </button>
              <button class="p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded transition-colors duration-200"
                      onclick="UIManager.copyToClipboard('${t.url}')"
                      title="Copy link">
                <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                </svg>
              </button>
            </div>
          </div>
        </article>
      `}).join("")}}static updateSearchInfo(e){if(!s.searchInfo)return;const t=(e.search_time/1e6).toFixed(0),a=e.provider_stats||[],r=e.processing_time,d=a.filter(i=>i.success);a.reduce((i,h)=>i+h.result_count,0);const p=d.map(i=>{const h=(i.response_time/1e6).toFixed(0),g=i.result_count===1?"result":"results";return`${i.name}: ${i.result_count} ${g} (${h}ms)`}).join(" • ");let o="";if(r){const i=(r.search_time/1e6).toFixed(0),h=(r.deduplication_time/1e6).toFixed(0),g=(r.ranking_time/1e6).toFixed(0),y=(r.enhancement_time/1e6).toFixed(0);o=`
        <div class="text-xs text-gray-500 mt-1 space-y-1">
          <div class="flex flex-wrap gap-3">
            <span>Search: ${i}ms</span>
            <span>Dedup: ${h}ms</span>
            <span>Rank: ${g}ms</span>
            <span>Enhance: ${y}ms</span>
          </div>
          <div class="text-gray-400">${p}</div>
        </div>
      `}s.searchInfo.innerHTML=`
      <div class="flex items-center justify-between">
        <div>
          About ${e.total_count.toLocaleString()} results from ${d.length} providers (${t}ms)
        </div>
        <button id="toggle-stats" class="text-xs text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300">
          Stats
        </button>
      </div>
      <div id="detailed-stats" class="hidden">
        ${o}
      </div>
    `;const n=s.searchInfo.querySelector("#toggle-stats"),u=s.searchInfo.querySelector("#detailed-stats");n&&u&&n.addEventListener("click",()=>{u.classList.contains("hidden")?(u.classList.remove("hidden"),n.textContent="Hide"):(u.classList.add("hidden"),n.textContent="Stats")}),s.searchInfo.classList.remove("hidden")}static updatePagination(e,t){if(!s.pagination)return;const a=Math.ceil(e.total_count/e.per_page);if(a<=1){s.pagination.classList.add("hidden");return}let r="";e.page>1&&(r+=`<button data-page="${e.page-1}" class="pagination-btn px-3 py-2 text-blue-600 hover:underline">Previous</button>`);const d=Math.max(1,e.page-2),p=Math.min(a,e.page+2);for(let o=d;o<=p;o++){const n=o===e.page;r+=`<button data-page="${o}" class="pagination-btn px-3 py-2 ${n?"font-bold text-black":"text-blue-600 hover:underline"}">${o}</button>`}e.page<a&&(r+=`<button data-page="${e.page+1}" class="pagination-btn px-3 py-2 text-blue-600 hover:underline">Next</button>`),s.pagination.innerHTML=r,s.pagination.querySelectorAll(".pagination-btn").forEach(o=>{o.addEventListener("click",n=>{const u=parseInt(n.target.dataset.page||"1");t(u)})}),s.pagination.classList.remove("hidden")}static getProviderColor(e){return{google:"blue-500",bing:"orange-500",duckduckgo:"red-500",yahoo:"purple-500",startpage:"green-500",brave:"orange-600"}[e.toLowerCase()]||"gray-500"}static async shareResult(e,t){if(navigator.share)try{await navigator.share({title:e,url:t})}catch{this.copyToClipboard(t)}else this.copyToClipboard(t)}static async copyToClipboard(e){try{await navigator.clipboard.writeText(e),this.showToast("Link copied to clipboard!")}catch{const a=document.createElement("textarea");a.value=e,document.body.appendChild(a),a.select(),document.execCommand("copy"),document.body.removeChild(a),this.showToast("Link copied to clipboard!")}}static showToast(e){const t=document.createElement("div");t.className="fixed bottom-20 right-6 bg-gray-900 dark:bg-gray-100 text-white dark:text-gray-900 px-4 py-2 rounded-lg shadow-lg z-50 transition-all duration-300 transform translate-y-full opacity-0",t.textContent=e,document.body.appendChild(t),setTimeout(()=>{t.classList.remove("translate-y-full","opacity-0")},100),setTimeout(()=>{t.classList.add("translate-y-full","opacity-0"),setTimeout(()=>{document.body.removeChild(t)},300)},3e3)}}class x{state={currentQuery:"",currentPage:1,isLoading:!1};autocompleteTimeout;constructor(){this.initializeEventListeners(),this.handleURLParams()}initializeEventListeners(){s.searchForm?.addEventListener("submit",this.handleSearch.bind(this)),s.quickSearchButtons.forEach(e=>{e.addEventListener("click",this.handleQuickSearch.bind(this))}),s.searchInput?.addEventListener("input",this.handleAutocompleteInput.bind(this)),document.addEventListener("click",this.handleDocumentClick.bind(this))}async handleSearch(e){e.preventDefault();const t=s.searchInput?.value?.trim();!t||this.state.isLoading||(this.state.currentQuery=t,this.state.currentPage=1,await this.performSearch(t,1))}handleQuickSearch(e){const a=e.target.getAttribute("data-query");a&&s.searchInput&&(s.searchInput.value=a,this.state.currentQuery=a,this.state.currentPage=1,this.performSearch(a,1))}handleAutocompleteInput(e){const a=e.target.value.trim();this.autocompleteTimeout&&clearTimeout(this.autocompleteTimeout),a.length>=2?this.autocompleteTimeout=setTimeout(()=>{this.fetchAutocomplete(a)},300):this.hideAutocomplete()}handleDocumentClick(e){const t=e.target;s.searchForm&&!s.searchForm.contains(t)&&this.hideAutocomplete()}async performSearch(e,t=1){if(!this.state.isLoading){this.state.isLoading=!0,l.showLoading(),this.hideAutocomplete();try{const a=await f.search(e,t);l.displayResults(a),l.updateSearchInfo(a),l.updatePagination(a,this.goToPage.bind(this)),this.state.currentPage=t,this.updateURL(e,t)}catch(a){console.error("Search error:",a),l.displayError("Search failed. Please try again.")}finally{this.state.isLoading=!1,l.hideLoading()}}}async fetchAutocomplete(e){try{const t=await f.autocomplete(e);this.displayAutocomplete(t.suggestions)}catch(t){console.error("Autocomplete error:",t)}}displayAutocomplete(e){if(!e||e.length===0||!s.autocomplete){this.hideAutocomplete();return}s.autocomplete.innerHTML=e.map(t=>`<div class="px-4 py-2 hover:bg-gray-100 cursor-pointer autocomplete-item" data-suggestion="${t}">
        ${t}
      </div>`).join(""),s.autocomplete.querySelectorAll(".autocomplete-item").forEach(t=>{t.addEventListener("click",()=>{const a=t.getAttribute("data-suggestion");a&&s.searchInput&&s.searchForm&&(s.searchInput.value=a,this.hideAutocomplete(),s.searchForm.dispatchEvent(new Event("submit")))})}),s.autocomplete.classList.remove("hidden")}hideAutocomplete(){s.autocomplete?.classList.add("hidden")}async goToPage(e){this.state.currentQuery&&(await this.performSearch(this.state.currentQuery,e),window.scrollTo({top:0,behavior:"smooth"}))}updateURL(e,t){const a=new URL(window.location.href);a.searchParams.set("q",e),t>1?a.searchParams.set("page",t.toString()):a.searchParams.delete("page"),window.history.replaceState({},"",a.toString())}handleURLParams(){const e=new URLSearchParams(window.location.search),t=e.get("q"),a=e.get("page"),r=a?parseInt(a):1;t&&s.searchInput&&(s.searchInput.value=t,this.state.currentQuery=t,this.state.currentPage=r,this.performSearch(t,r))}}new x;
