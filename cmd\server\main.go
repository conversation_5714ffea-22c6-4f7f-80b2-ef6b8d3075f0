package main

import (
	"context"
	"flag"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"ultra_search/internal/aggregator"
	"ultra_search/internal/api"
	"ultra_search/internal/cache"
	"ultra_search/internal/config"
	"ultra_search/internal/metrics"
	"ultra_search/internal/models"
	"ultra_search/internal/search"
)

func main() {
	// Parse command line flags
	var devMode bool
	flag.BoolVar(&devMode, "dev", false, "Enable development mode with hot reload")
	flag.Parse()

	// Load configuration
	cfg := config.Load()

	// Initialize performance metrics
	performanceMetrics := metrics.NewPerformanceMetrics()

	// Initialize cache manager
	cacheManager := cache.NewCacheManager(
		cfg.Cache.TTL,
		cfg.Cache.TTL*2, // Autocomplete cache TTL (longer)
		cfg.Cache.CleanupInterval,
	)

	// Initialize search providers
	providers := initializeProviders(cfg)

	// Initialize search aggregator
	searchAggregator := aggregator.NewSearchAggregator(
		providers,
		cfg.Search.MaxConcurrentProviders,
		cfg.Search.SearchTimeout,
	)

	// Setup router with development mode
	router := api.SetupRouter(searchAggregator, cacheManager, cfg, performanceMetrics, devMode)

	// Create HTTP server
	server := &http.Server{
		Addr:         fmt.Sprintf("%s:%s", cfg.Server.Host, cfg.Server.Port),
		Handler:      router,
		ReadTimeout:  cfg.Server.ReadTimeout,
		WriteTimeout: cfg.Server.WriteTimeout,
		IdleTimeout:  cfg.Server.IdleTimeout,
	}

	// Start server in a goroutine
	go func() {
		log.Printf("Starting Ultra Search server on %s:%s", cfg.Server.Host, cfg.Server.Port)
		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatalf("Failed to start server: %v", err)
		}
	}()

	// Wait for interrupt signal to gracefully shutdown the server
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	log.Println("Shutting down server...")

	// Give outstanding requests 30 seconds to complete
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	if err := server.Shutdown(ctx); err != nil {
		log.Fatalf("Server forced to shutdown: %v", err)
	}

	log.Println("Server exited")
}

// initializeProviders creates and returns available search providers
func initializeProviders(cfg *config.Config) []models.SearchProvider {
	var providers []models.SearchProvider

	// Initialize providers based on configuration
	for _, providerName := range cfg.Search.EnabledProviders {
		switch providerName {
		case "duckduckgo":
			provider := search.NewDuckDuckGoProvider()
			if provider.IsAvailable() {
				providers = append(providers, provider)
				log.Printf("Initialized DuckDuckGo provider")
			} else {
				log.Printf("DuckDuckGo provider is not available")
			}

		case "bing":
			provider := search.NewBingProvider()
			if provider.IsAvailable() {
				providers = append(providers, provider)
				log.Printf("Initialized Bing provider")
			} else {
				log.Printf("Bing provider is not available")
			}

		case "google":
			provider := search.NewGoogleProvider()
			if provider.IsAvailable() {
				providers = append(providers, provider)
				log.Printf("Initialized Google provider")
			} else {
				log.Printf("Google provider is not available")
			}

		case "yahoo":
			provider := search.NewYahooProvider()
			if provider.IsAvailable() {
				providers = append(providers, provider)
				log.Printf("Initialized Yahoo provider")
			} else {
				log.Printf("Yahoo provider is not available")
			}

		case "startpage":
			provider := search.NewStartpageProvider()
			if provider.IsAvailable() {
				providers = append(providers, provider)
				log.Printf("Initialized Startpage provider")
			} else {
				log.Printf("Startpage provider is not available")
			}

		case "brave":
			provider := search.NewBraveProvider()
			if provider.IsAvailable() {
				providers = append(providers, provider)
				log.Printf("Initialized Brave provider")
			} else {
				log.Printf("Brave provider is not available")
			}

		default:
			log.Printf("Unknown provider: %s", providerName)
		}
	}

	if len(providers) == 0 {
		log.Fatal("No search providers available")
	}

	log.Printf("Initialized %d search providers", len(providers))
	return providers
}
