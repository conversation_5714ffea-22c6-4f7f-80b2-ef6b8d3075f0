package aggregator

import (
	"context"
	"crypto/md5"
	"fmt"
	"net/url"
	"sort"
	"strings"
	"sync"
	"time"

	"ultra_search/internal/models"
)

// SearchAggregator handles aggregation of search results from multiple providers
type SearchAggregator struct {
	providers    []models.SearchProvider
	maxProviders int
	timeout      time.Duration
}

// NewSearchAggregator creates a new search aggregator
func NewSearchAggregator(providers []models.SearchProvider, maxProviders int, timeout time.Duration) *SearchAggregator {
	return &SearchAggregator{
		providers:    providers,
		maxProviders: maxProviders,
		timeout:      timeout,
	}
}

// Search performs concurrent searches across all providers and aggregates results
func (sa *SearchAggregator) Search(ctx context.Context, request models.SearchRequest) (*models.SearchResponse, error) {
	startTime := time.Now()

	// Create context with timeout
	searchCtx, cancel := context.WithTimeout(ctx, sa.timeout)
	defer cancel()

	// Channel to collect results from providers
	resultsChan := make(chan models.ProviderResult, len(sa.providers))

	// Use a semaphore to limit concurrent providers
	semaphore := make(chan struct{}, sa.maxProviders)
	var wg sync.WaitGroup

	// Start searches concurrently with enhanced performance
	for _, provider := range sa.providers {
		if !provider.IsAvailable() {
			continue
		}

		wg.Add(1)
		go func(p models.SearchProvider) {
			defer wg.Done()

			// Acquire semaphore with timeout
			select {
			case semaphore <- struct{}{}:
				defer func() { <-semaphore }()
			case <-searchCtx.Done():
				return
			case <-time.After(1 * time.Second):
				// Skip if can't acquire semaphore quickly
				return
			}

			// Adaptive rate limiting based on provider performance
			rateLimit := p.GetRateLimit()
			if rateLimit > 3*time.Second {
				rateLimit = 3 * time.Second // Cap maximum rate limit
			}

			select {
			case <-time.After(rateLimit):
			case <-searchCtx.Done():
				return
			}

			providerStart := time.Now()
			results, err := p.Search(request)
			duration := time.Since(providerStart)

			// Filter and enhance results
			if err == nil && len(results) > 0 {
				results = sa.enhanceResults(results, p.Name())
			}

			select {
			case resultsChan <- models.ProviderResult{
				Provider: p.Name(),
				Results:  results,
				Error:    err,
				Duration: duration,
			}:
			case <-searchCtx.Done():
				return
			}
		}(provider)
	}

	// Close results channel when all goroutines complete
	go func() {
		wg.Wait()
		close(resultsChan)
	}()

	// Collect results
	var allResults []models.SearchResult
	var sources []string
	var errors []error

	for result := range resultsChan {
		sources = append(sources, result.Provider)

		if result.Error != nil {
			errors = append(errors, result.Error)
			continue
		}

		allResults = append(allResults, result.Results...)
	}

	// If all providers failed, return error
	if len(allResults) == 0 && len(errors) > 0 {
		return nil, fmt.Errorf("all search providers failed: %v", errors)
	}

	// Deduplicate and rank results
	deduplicatedResults := sa.deduplicateResults(allResults)
	rankedResults := sa.rankResults(deduplicatedResults, request.Query)

	// Apply pagination
	page := request.Page
	if page < 1 {
		page = 1
	}
	perPage := request.PerPage
	if perPage < 1 {
		perPage = 10
	}
	if perPage > 50 {
		perPage = 50
	}

	start := (page - 1) * perPage
	end := start + perPage
	if end > len(rankedResults) {
		end = len(rankedResults)
	}

	var paginatedResults []models.SearchResult
	if start < len(rankedResults) {
		paginatedResults = rankedResults[start:end]
	}

	// Generate suggestions (simple implementation)
	suggestions := sa.generateSuggestions(request.Query, rankedResults)

	searchTime := time.Since(startTime)

	return &models.SearchResponse{
		Query:       request.Query,
		Results:     paginatedResults,
		TotalCount:  len(rankedResults),
		Page:        page,
		PerPage:     perPage,
		Sources:     sources,
		SearchTime:  searchTime,
		Suggestions: suggestions,
	}, nil
}

// deduplicateResults removes duplicate results based on URL similarity
func (sa *SearchAggregator) deduplicateResults(results []models.SearchResult) []models.SearchResult {
	seen := make(map[string]bool)
	var deduplicated []models.SearchResult

	for _, result := range results {
		// Normalize URL for comparison
		normalizedURL := sa.normalizeURL(result.URL)
		urlHash := sa.hashString(normalizedURL)

		if !seen[urlHash] {
			seen[urlHash] = true
			deduplicated = append(deduplicated, result)
		} else {
			// If we've seen this URL, check if this result has a better score
			for i, existing := range deduplicated {
				if sa.hashString(sa.normalizeURL(existing.URL)) == urlHash {
					if result.Score > existing.Score {
						deduplicated[i] = result
					}
					break
				}
			}
		}
	}

	return deduplicated
}

// normalizeURL normalizes URLs for comparison
func (sa *SearchAggregator) normalizeURL(rawURL string) string {
	parsed, err := url.Parse(rawURL)
	if err != nil {
		return rawURL
	}

	// Remove common tracking parameters
	query := parsed.Query()
	trackingParams := []string{"utm_source", "utm_medium", "utm_campaign", "utm_term", "utm_content", "ref", "source"}
	for _, param := range trackingParams {
		query.Del(param)
	}
	parsed.RawQuery = query.Encode()

	// Remove fragment
	parsed.Fragment = ""

	// Normalize scheme
	if parsed.Scheme == "" {
		parsed.Scheme = "https"
	}

	// Remove trailing slash
	parsed.Path = strings.TrimSuffix(parsed.Path, "/")

	return parsed.String()
}

// rankResults ranks results using a combination of factors
func (sa *SearchAggregator) rankResults(results []models.SearchResult, query string) []models.SearchResult {
	queryTerms := strings.Fields(strings.ToLower(query))

	for i := range results {
		score := results[i].Score

		// Boost score based on title relevance
		titleScore := sa.calculateRelevanceScore(strings.ToLower(results[i].Title), queryTerms)
		score += titleScore * 0.3

		// Boost score based on description relevance
		descScore := sa.calculateRelevanceScore(strings.ToLower(results[i].Description), queryTerms)
		score += descScore * 0.2

		// Boost score based on URL relevance
		urlScore := sa.calculateRelevanceScore(strings.ToLower(results[i].URL), queryTerms)
		score += urlScore * 0.1

		// Apply source-based scoring
		switch results[i].Source {
		case "google":
			score *= 1.1 // Google results get a slight boost
		case "duckduckgo":
			score *= 1.05 // DuckDuckGo gets a small boost
		case "bing":
			score *= 1.0 // Bing baseline
		}

		// Apply recency boost (newer results get slight preference)
		age := time.Since(results[i].Timestamp)
		if age < time.Hour {
			score *= 1.02
		}

		results[i].Score = score
	}

	// Sort by score (descending)
	sort.Slice(results, func(i, j int) bool {
		return results[i].Score > results[j].Score
	})

	// Update ranks
	for i := range results {
		results[i].Rank = i + 1
	}

	return results
}

// calculateRelevanceScore calculates relevance score based on term matching
func (sa *SearchAggregator) calculateRelevanceScore(text string, queryTerms []string) float64 {
	if len(queryTerms) == 0 {
		return 0
	}

	matches := 0
	for _, term := range queryTerms {
		if strings.Contains(text, term) {
			matches++
		}
	}

	return float64(matches) / float64(len(queryTerms))
}

// generateSuggestions generates search suggestions based on results
func (sa *SearchAggregator) generateSuggestions(query string, results []models.SearchResult) []string {
	var suggestions []string
	suggestionSet := make(map[string]bool)

	// Extract common terms from top results
	for i, result := range results {
		if i >= 5 { // Only look at top 5 results
			break
		}

		words := strings.Fields(strings.ToLower(result.Title))
		for _, word := range words {
			if len(word) > 3 && !strings.Contains(strings.ToLower(query), word) {
				suggestion := query + " " + word
				if !suggestionSet[suggestion] && len(suggestions) < 5 {
					suggestions = append(suggestions, suggestion)
					suggestionSet[suggestion] = true
				}
			}
		}
	}

	return suggestions
}

// hashString creates a hash of a string
func (sa *SearchAggregator) hashString(s string) string {
	hash := md5.Sum([]byte(s))
	return fmt.Sprintf("%x", hash)
}

// enhanceResults filters and enhances search results
func (sa *SearchAggregator) enhanceResults(results []models.SearchResult, providerName string) []models.SearchResult {
	var enhanced []models.SearchResult

	for _, result := range results {
		// Skip results with empty or very short titles
		if len(strings.TrimSpace(result.Title)) < 3 {
			continue
		}

		// Skip results with empty URLs
		if result.URL == "" {
			continue
		}

		// Skip obvious spam or low-quality results
		if sa.isLowQuality(result) {
			continue
		}

		// Enhance the result
		result.Score = sa.calculateEnhancedScore(result, providerName)

		enhanced = append(enhanced, result)
	}

	return enhanced
}

// isLowQuality checks if a result appears to be low quality or spam
func (sa *SearchAggregator) isLowQuality(result models.SearchResult) bool {
	title := strings.ToLower(result.Title)
	description := strings.ToLower(result.Description)

	// Check for spam indicators
	spamKeywords := []string{
		"click here", "free download", "100% free", "no registration",
		"instant download", "limited time", "act now", "special offer",
	}

	for _, keyword := range spamKeywords {
		if strings.Contains(title, keyword) || strings.Contains(description, keyword) {
			return true
		}
	}

	// Check for excessive repetition
	words := strings.Fields(title)
	if len(words) > 3 {
		wordCount := make(map[string]int)
		for _, word := range words {
			if len(word) > 2 {
				wordCount[word]++
			}
		}

		for _, count := range wordCount {
			if count > len(words)/2 {
				return true // Too much repetition
			}
		}
	}

	return false
}

// calculateEnhancedScore calculates an enhanced relevance score
func (sa *SearchAggregator) calculateEnhancedScore(result models.SearchResult, providerName string) float64 {
	score := result.Score

	// Boost score based on provider reliability
	providerBoost := map[string]float64{
		"google":     0.05,
		"bing":       0.03,
		"duckduckgo": 0.02,
		"yahoo":      0.02,
		"startpage":  0.04,
		"brave":      0.03,
	}

	if boost, exists := providerBoost[providerName]; exists {
		score += boost
	}

	// Boost score for HTTPS URLs
	if strings.HasPrefix(result.URL, "https://") {
		score += 0.01
	}

	// Boost score for well-known domains
	wellKnownDomains := []string{
		"wikipedia.org", "github.com", "stackoverflow.com", "reddit.com",
		"medium.com", "dev.to", "mozilla.org", "w3.org", "ietf.org",
	}

	for _, domain := range wellKnownDomains {
		if strings.Contains(result.URL, domain) {
			score += 0.02
			break
		}
	}

	// Penalize very long titles (likely spam)
	if len(result.Title) > 100 {
		score -= 0.02
	}

	// Ensure score stays within bounds
	if score > 1.0 {
		score = 1.0
	}
	if score < 0.0 {
		score = 0.0
	}

	return score
}
