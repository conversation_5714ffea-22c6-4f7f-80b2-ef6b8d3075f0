---
// Modern loading spinner with enhanced animations
---

<div id="loading" class="hidden text-center py-12 fade-in">
  <!-- Modern loading animation -->
  <div class="flex justify-center items-center mb-6">
    <div class="relative">
      <!-- Outer ring -->
      <div class="w-16 h-16 border-4 border-blue-200 dark:border-blue-800 rounded-full animate-pulse"></div>
      <!-- Inner spinning ring -->
      <div class="absolute top-0 left-0 w-16 h-16 border-4 border-transparent border-t-blue-600 dark:border-t-blue-400 rounded-full animate-spin"></div>
      <!-- Center dot -->
      <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-3 h-3 bg-blue-600 dark:bg-blue-400 rounded-full animate-pulse"></div>
    </div>
  </div>

  <!-- Loading text with typing animation -->
  <div class="space-y-3">
    <p class="text-lg font-medium text-gray-800 dark:text-gray-200">
      <span id="loading-text">Searching</span>
      <span class="inline-flex">
        <span class="w-1 h-5 bg-blue-600 dark:bg-blue-400 rounded-full pulse-dot"></span>
        <span class="w-1 h-5 bg-blue-600 dark:bg-blue-400 rounded-full pulse-dot ml-1"></span>
        <span class="w-1 h-5 bg-blue-600 dark:bg-blue-400 rounded-full pulse-dot ml-1"></span>
      </span>
    </p>

    <!-- Progress indicators -->
    <div class="max-w-md mx-auto">
      <div class="flex justify-between text-xs text-gray-500 dark:text-gray-400 mb-2">
        <span>Querying providers</span>
        <span id="progress-text">0%</span>
      </div>
      <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 overflow-hidden">
        <div id="progress-bar" class="h-2 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full transition-all duration-500 ease-out" style="width: 0%"></div>
      </div>
    </div>

    <!-- Provider status indicators -->
    <div id="provider-status" class="flex flex-wrap justify-center gap-2 mt-4">
      <!-- Provider indicators will be populated by JavaScript -->
    </div>
  </div>
</div>

<!-- Skeleton loading for results -->
<div id="skeleton-loading" class="hidden max-w-2xl mx-auto space-y-6 py-8">
  <div class="animate-pulse">
    <!-- Search info skeleton -->
    <div class="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/3 mb-6"></div>

    <!-- Result skeletons -->
    <div class="space-y-6">
      <div class="space-y-3" v-for="i in 3">
        <div class="h-3 bg-gray-200 dark:bg-gray-700 rounded w-1/4"></div>
        <div class="h-5 bg-gray-200 dark:bg-gray-700 rounded w-3/4"></div>
        <div class="space-y-2">
          <div class="h-3 bg-gray-200 dark:bg-gray-700 rounded"></div>
          <div class="h-3 bg-gray-200 dark:bg-gray-700 rounded w-5/6"></div>
        </div>
        <div class="h-3 bg-gray-200 dark:bg-gray-700 rounded w-1/3"></div>
      </div>
    </div>
  </div>
</div>

<script>
  // Enhanced loading animations
  let loadingInterval: number;
  let progressInterval: number;

  const loadingTexts = [
    'Searching',
    'Querying providers',
    'Aggregating results',
    'Ranking content',
    'Almost done'
  ];

  function startLoadingAnimation() {
    const loadingTextEl = document.getElementById('loading-text');
    const progressBar = document.getElementById('progress-bar');
    const progressText = document.getElementById('progress-text');

    let textIndex = 0;
    let progress = 0;

    // Animate loading text
    loadingInterval = setInterval(() => {
      if (loadingTextEl) {
        loadingTextEl.textContent = loadingTexts[textIndex];
        textIndex = (textIndex + 1) % loadingTexts.length;
      }
    }, 1500);

    // Animate progress bar
    progressInterval = setInterval(() => {
      progress = Math.min(progress + Math.random() * 15 + 5, 95);
      if (progressBar) {
        progressBar.style.width = `${progress}%`;
      }
      if (progressText) {
        progressText.textContent = `${Math.round(progress)}%`;
      }

      if (progress >= 95) {
        clearInterval(progressInterval);
      }
    }, 300);
  }

  function stopLoadingAnimation() {
    clearInterval(loadingInterval);
    clearInterval(progressInterval);

    // Complete the progress bar
    const progressBar = document.getElementById('progress-bar');
    const progressText = document.getElementById('progress-text');
    if (progressBar) progressBar.style.width = '100%';
    if (progressText) progressText.textContent = '100%';
  }

  // Export functions for use by search manager
  (window as any).loadingAnimation = {
    start: startLoadingAnimation,
    stop: stopLoadingAnimation
  };
</script>
