const h="http://localhost:8080/api/v1";class u{static async search(e,t=1){const s=await fetch(`${h}/search?q=${encodeURIComponent(e)}&page=${t}&per_page=10`);if(!s.ok)throw new Error(`Search failed: ${s.status}`);return s.json()}static async autocomplete(e){const t=await fetch(`${h}/autocomplete?q=${encodeURIComponent(e)}&limit=5`);if(!t.ok)throw new Error(`Autocomplete failed: ${t.status}`);return t.json()}}class r{static cache=new Map;static get(e){return this.cache.has(e)||this.cache.set(e,document.querySelector(e)),this.cache.get(e)}static getAll(e){return document.querySelectorAll(e)}static clear(){this.cache.clear()}}const a={get searchForm(){return r.get("#search-form")},get searchInput(){return r.get("#search-input")},get autocomplete(){return r.get("#autocomplete")},get loading(){return r.get("#loading")},get results(){return r.get("#search-results")},get searchInfo(){return r.get("#search-info")},get pagination(){return r.get("#pagination")},get quickSearchButtons(){return r.getAll(".quick-search")}};class o{static showLoading(){a.loading?.classList.remove("hidden"),a.results&&(a.results.innerHTML=""),a.searchInfo?.classList.add("hidden"),a.pagination?.classList.add("hidden")}static hideLoading(){a.loading?.classList.add("hidden")}static displayError(e){a.results&&(a.results.innerHTML=`
        <div class="text-center py-16">
          <div class="w-16 h-16 mx-auto mb-4 bg-red-100 rounded-full flex items-center justify-center">
            <svg class="w-8 h-8 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
          </div>
          <p class="text-red-500 text-lg font-medium">${e}</p>
          <p class="text-slate-400 text-sm mt-1">Please try again</p>
        </div>
      `),a.searchInfo?.classList.add("hidden"),a.pagination?.classList.add("hidden")}static displayResults(e){if(a.results){if(!e.results||e.results.length===0){a.results.innerHTML=`
        <div class="text-center py-8">
          <p class="text-gray-600 dark:text-gray-400">No results found</p>
        </div>
      `;return}a.results.innerHTML=e.results.map(t=>`
        <div class="mb-6">
          <div class="text-sm text-green-700 dark:text-green-400 mb-1">${new URL(t.url).hostname}</div>
          <h3 class="text-xl text-blue-600 dark:text-blue-400 hover:underline mb-1">
            <a href="${t.url}" target="_blank" rel="noopener noreferrer">
              ${t.title}
            </a>
          </h3>
          <p class="text-gray-600 dark:text-gray-400 text-sm leading-relaxed">${t.description}</p>
          <div class="text-xs text-gray-500 mt-1">
            ${t.source} • Score: ${t.score.toFixed(2)}
          </div>
        </div>
      `).join("")}}static updateSearchInfo(e){if(!a.searchInfo)return;const t=(e.search_time/1e6).toFixed(0);a.searchInfo.innerHTML=`
      About ${e.total_count.toLocaleString()} results (${t}ms)
    `,a.searchInfo.classList.remove("hidden")}static updatePagination(e,t){if(!a.pagination)return;const s=Math.ceil(e.total_count/e.per_page);if(s<=1){a.pagination.classList.add("hidden");return}let i="";e.page>1&&(i+=`<button data-page="${e.page-1}" class="pagination-btn px-3 py-2 text-blue-600 hover:underline">Previous</button>`);const d=Math.max(1,e.page-2),p=Math.min(s,e.page+2);for(let n=d;n<=p;n++){const l=n===e.page;i+=`<button data-page="${n}" class="pagination-btn px-3 py-2 ${l?"font-bold text-black":"text-blue-600 hover:underline"}">${n}</button>`}e.page<s&&(i+=`<button data-page="${e.page+1}" class="pagination-btn px-3 py-2 text-blue-600 hover:underline">Next</button>`),a.pagination.innerHTML=i,a.pagination.querySelectorAll(".pagination-btn").forEach(n=>{n.addEventListener("click",l=>{const g=parseInt(l.target.dataset.page||"1");t(g)})}),a.pagination.classList.remove("hidden")}}class m{state={currentQuery:"",currentPage:1,isLoading:!1};autocompleteTimeout;constructor(){this.initializeEventListeners(),this.handleURLParams()}initializeEventListeners(){a.searchForm?.addEventListener("submit",this.handleSearch.bind(this)),a.quickSearchButtons.forEach(e=>{e.addEventListener("click",this.handleQuickSearch.bind(this))}),a.searchInput?.addEventListener("input",this.handleAutocompleteInput.bind(this)),document.addEventListener("click",this.handleDocumentClick.bind(this))}async handleSearch(e){e.preventDefault();const t=a.searchInput?.value?.trim();!t||this.state.isLoading||(this.state.currentQuery=t,this.state.currentPage=1,await this.performSearch(t,1))}handleQuickSearch(e){const s=e.target.getAttribute("data-query");s&&a.searchInput&&(a.searchInput.value=s,this.state.currentQuery=s,this.state.currentPage=1,this.performSearch(s,1))}handleAutocompleteInput(e){const s=e.target.value.trim();this.autocompleteTimeout&&clearTimeout(this.autocompleteTimeout),s.length>=2?this.autocompleteTimeout=setTimeout(()=>{this.fetchAutocomplete(s)},300):this.hideAutocomplete()}handleDocumentClick(e){const t=e.target;a.searchForm&&!a.searchForm.contains(t)&&this.hideAutocomplete()}async performSearch(e,t=1){if(!this.state.isLoading){this.state.isLoading=!0,o.showLoading(),this.hideAutocomplete();try{const s=await u.search(e,t);o.displayResults(s),o.updateSearchInfo(s),o.updatePagination(s,this.goToPage.bind(this)),this.state.currentPage=t,this.updateURL(e,t)}catch(s){console.error("Search error:",s),o.displayError("Search failed. Please try again.")}finally{this.state.isLoading=!1,o.hideLoading()}}}async fetchAutocomplete(e){try{const t=await u.autocomplete(e);this.displayAutocomplete(t.suggestions)}catch(t){console.error("Autocomplete error:",t)}}displayAutocomplete(e){if(!e||e.length===0||!a.autocomplete){this.hideAutocomplete();return}a.autocomplete.innerHTML=e.map(t=>`<div class="px-4 py-2 hover:bg-gray-100 cursor-pointer autocomplete-item" data-suggestion="${t}">
        ${t}
      </div>`).join(""),a.autocomplete.querySelectorAll(".autocomplete-item").forEach(t=>{t.addEventListener("click",()=>{const s=t.getAttribute("data-suggestion");s&&a.searchInput&&a.searchForm&&(a.searchInput.value=s,this.hideAutocomplete(),a.searchForm.dispatchEvent(new Event("submit")))})}),a.autocomplete.classList.remove("hidden")}hideAutocomplete(){a.autocomplete?.classList.add("hidden")}async goToPage(e){this.state.currentQuery&&(await this.performSearch(this.state.currentQuery,e),window.scrollTo({top:0,behavior:"smooth"}))}updateURL(e,t){const s=new URL(window.location.href);s.searchParams.set("q",e),t>1?s.searchParams.set("page",t.toString()):s.searchParams.delete("page"),window.history.replaceState({},"",s.toString())}handleURLParams(){const e=new URLSearchParams(window.location.search),t=e.get("q"),s=e.get("page"),i=s?parseInt(s):1;t&&a.searchInput&&(a.searchInput.value=t,this.state.currentQuery=t,this.state.currentPage=i,this.performSearch(t,i))}}new m;
