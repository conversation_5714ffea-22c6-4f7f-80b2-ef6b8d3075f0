const f="http://localhost:8080/api/v1";class v{static async search(e,t=1){const a=await fetch(`${f}/search?q=${encodeURIComponent(e)}&page=${t}&per_page=10`);if(!a.ok)throw new Error(`Search failed: ${a.status}`);return a.json()}static async autocomplete(e){const t=await fetch(`${f}/autocomplete?q=${encodeURIComponent(e)}&limit=5`);if(!t.ok)throw new Error(`Autocomplete failed: ${t.status}`);return t.json()}}class c{static cache=new Map;static get(e){return this.cache.has(e)||this.cache.set(e,document.querySelector(e)),this.cache.get(e)}static getAll(e){return document.querySelectorAll(e)}static clear(){this.cache.clear()}}const s={get searchForm(){return c.get("#search-form")},get searchInput(){return c.get("#search-input")},get autocomplete(){return c.get("#autocomplete")},get loading(){return c.get("#loading")},get results(){return c.get("#search-results")},get searchInfo(){return c.get("#search-info")},get pagination(){return c.get("#pagination")},get quickSearchButtons(){return c.getAll(".quick-search")}};class d{static showLoading(){s.loading?.classList.remove("hidden"),s.results&&(s.results.innerHTML=""),s.searchInfo?.classList.add("hidden"),s.pagination?.classList.add("hidden")}static hideLoading(){s.loading?.classList.add("hidden")}static displayError(e){s.results&&(s.results.innerHTML=`
        <div class="text-center py-16">
          <div class="w-16 h-16 mx-auto mb-4 bg-red-100 rounded-full flex items-center justify-center">
            <svg class="w-8 h-8 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
          </div>
          <p class="text-red-500 text-lg font-medium">${e}</p>
          <p class="text-slate-400 text-sm mt-1">Please try again</p>
        </div>
      `),s.searchInfo?.classList.add("hidden"),s.pagination?.classList.add("hidden")}static displayResults(e){if(s.results){if(!e.results||e.results.length===0){s.results.innerHTML=`
        <div class="text-center py-8">
          <p class="text-gray-600 dark:text-gray-400">No results found</p>
        </div>
      `;return}s.results.innerHTML=e.results.map(t=>`
        <div class="mb-6">
          <div class="text-sm text-green-700 dark:text-green-400 mb-1">${new URL(t.url).hostname}</div>
          <h3 class="text-xl text-blue-600 dark:text-blue-400 hover:underline mb-1">
            <a href="${t.url}" target="_blank" rel="noopener noreferrer">
              ${t.title}
            </a>
          </h3>
          <p class="text-gray-600 dark:text-gray-400 text-sm leading-relaxed">${t.description}</p>
          <div class="text-xs text-gray-500 mt-1">
            ${t.source} • Score: ${t.score.toFixed(2)}
          </div>
        </div>
      `).join("")}}static updateSearchInfo(e){if(!s.searchInfo)return;const t=(e.search_time/1e6).toFixed(0),a=e.provider_stats||[],r=e.processing_time,h=a.filter(n=>n.success);a.reduce((n,u)=>n+u.result_count,0);const g=h.map(n=>{const u=(n.response_time/1e6).toFixed(0),m=n.result_count===1?"result":"results";return`${n.name}: ${n.result_count} ${m} (${u}ms)`}).join(" • ");let i="";if(r){const n=(r.search_time/1e6).toFixed(0),u=(r.deduplication_time/1e6).toFixed(0),m=(r.ranking_time/1e6).toFixed(0),x=(r.enhancement_time/1e6).toFixed(0);i=`
        <div class="text-xs text-gray-500 mt-1 space-y-1">
          <div class="flex flex-wrap gap-3">
            <span>Search: ${n}ms</span>
            <span>Dedup: ${u}ms</span>
            <span>Rank: ${m}ms</span>
            <span>Enhance: ${x}ms</span>
          </div>
          <div class="text-gray-400">${g}</div>
        </div>
      `}s.searchInfo.innerHTML=`
      <div class="flex items-center justify-between">
        <div>
          About ${e.total_count.toLocaleString()} results from ${h.length} providers (${t}ms)
        </div>
        <button id="toggle-stats" class="text-xs text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300">
          Stats
        </button>
      </div>
      <div id="detailed-stats" class="hidden">
        ${i}
      </div>
    `;const o=s.searchInfo.querySelector("#toggle-stats"),l=s.searchInfo.querySelector("#detailed-stats");o&&l&&o.addEventListener("click",()=>{l.classList.contains("hidden")?(l.classList.remove("hidden"),o.textContent="Hide"):(l.classList.add("hidden"),o.textContent="Stats")}),s.searchInfo.classList.remove("hidden")}static updatePagination(e,t){if(!s.pagination)return;const a=Math.ceil(e.total_count/e.per_page);if(a<=1){s.pagination.classList.add("hidden");return}let r="";e.page>1&&(r+=`<button data-page="${e.page-1}" class="pagination-btn px-3 py-2 text-blue-600 hover:underline">Previous</button>`);const h=Math.max(1,e.page-2),g=Math.min(a,e.page+2);for(let i=h;i<=g;i++){const o=i===e.page;r+=`<button data-page="${i}" class="pagination-btn px-3 py-2 ${o?"font-bold text-black":"text-blue-600 hover:underline"}">${i}</button>`}e.page<a&&(r+=`<button data-page="${e.page+1}" class="pagination-btn px-3 py-2 text-blue-600 hover:underline">Next</button>`),s.pagination.innerHTML=r,s.pagination.querySelectorAll(".pagination-btn").forEach(i=>{i.addEventListener("click",o=>{const l=parseInt(o.target.dataset.page||"1");t(l)})}),s.pagination.classList.remove("hidden")}}class y{state={currentQuery:"",currentPage:1,isLoading:!1};autocompleteTimeout;constructor(){this.initializeEventListeners(),this.handleURLParams()}initializeEventListeners(){s.searchForm?.addEventListener("submit",this.handleSearch.bind(this)),s.quickSearchButtons.forEach(e=>{e.addEventListener("click",this.handleQuickSearch.bind(this))}),s.searchInput?.addEventListener("input",this.handleAutocompleteInput.bind(this)),document.addEventListener("click",this.handleDocumentClick.bind(this))}async handleSearch(e){e.preventDefault();const t=s.searchInput?.value?.trim();!t||this.state.isLoading||(this.state.currentQuery=t,this.state.currentPage=1,await this.performSearch(t,1))}handleQuickSearch(e){const a=e.target.getAttribute("data-query");a&&s.searchInput&&(s.searchInput.value=a,this.state.currentQuery=a,this.state.currentPage=1,this.performSearch(a,1))}handleAutocompleteInput(e){const a=e.target.value.trim();this.autocompleteTimeout&&clearTimeout(this.autocompleteTimeout),a.length>=2?this.autocompleteTimeout=setTimeout(()=>{this.fetchAutocomplete(a)},300):this.hideAutocomplete()}handleDocumentClick(e){const t=e.target;s.searchForm&&!s.searchForm.contains(t)&&this.hideAutocomplete()}async performSearch(e,t=1){if(!this.state.isLoading){this.state.isLoading=!0,d.showLoading(),this.hideAutocomplete();try{const a=await v.search(e,t);d.displayResults(a),d.updateSearchInfo(a),d.updatePagination(a,this.goToPage.bind(this)),this.state.currentPage=t,this.updateURL(e,t)}catch(a){console.error("Search error:",a),d.displayError("Search failed. Please try again.")}finally{this.state.isLoading=!1,d.hideLoading()}}}async fetchAutocomplete(e){try{const t=await v.autocomplete(e);this.displayAutocomplete(t.suggestions)}catch(t){console.error("Autocomplete error:",t)}}displayAutocomplete(e){if(!e||e.length===0||!s.autocomplete){this.hideAutocomplete();return}s.autocomplete.innerHTML=e.map(t=>`<div class="px-4 py-2 hover:bg-gray-100 cursor-pointer autocomplete-item" data-suggestion="${t}">
        ${t}
      </div>`).join(""),s.autocomplete.querySelectorAll(".autocomplete-item").forEach(t=>{t.addEventListener("click",()=>{const a=t.getAttribute("data-suggestion");a&&s.searchInput&&s.searchForm&&(s.searchInput.value=a,this.hideAutocomplete(),s.searchForm.dispatchEvent(new Event("submit")))})}),s.autocomplete.classList.remove("hidden")}hideAutocomplete(){s.autocomplete?.classList.add("hidden")}async goToPage(e){this.state.currentQuery&&(await this.performSearch(this.state.currentQuery,e),window.scrollTo({top:0,behavior:"smooth"}))}updateURL(e,t){const a=new URL(window.location.href);a.searchParams.set("q",e),t>1?a.searchParams.set("page",t.toString()):a.searchParams.delete("page"),window.history.replaceState({},"",a.toString())}handleURLParams(){const e=new URLSearchParams(window.location.search),t=e.get("q"),a=e.get("page"),r=a?parseInt(a):1;t&&s.searchInput&&(s.searchInput.value=t,this.state.currentQuery=t,this.state.currentPage=r,this.performSearch(t,r))}}new y;
