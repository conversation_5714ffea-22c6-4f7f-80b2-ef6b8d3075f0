# Ultra Search Configuration

# Server Configuration
PORT=8080
HOST=0.0.0.0
READ_TIMEOUT=10s
WRITE_TIMEOUT=10s
IDLE_TIMEOUT=60s

# Search Configuration
MAX_CONCURRENT_PROVIDERS=5
SEARCH_TIMEOUT=8s
MAX_RESULTS_PER_PROVIDER=20
DEFAULT_RESULTS_PER_PAGE=10
MAX_RESULTS_PER_PAGE=50

# Cache Configuration
CACHE_TTL=5m
CACHE_CLEANUP_INTERVAL=10m
CACHE_MAX_SIZE=1000

# Rate Limiting
RATE_LIMIT_RPM=60
RATE_LIMIT_BURST=10

# Logging
LOG_LEVEL=info
LOG_FORMAT=json

# Search Providers (comma-separated list)
# Available providers: duckduckgo, bing, google, yahoo, startpage, brave
ENABLED_PROVIDERS=duckduckgo,bing,google,yahoo,startpage,brave

# API Keys (if using official APIs instead of mock implementations)
# GOOGLE_API_KEY=your_google_api_key_here
# GOOGLE_SEARCH_ENGINE_ID=your_search_engine_id_here
# BING_API_KEY=your_bing_api_key_here

# Frontend Configuration
FRONTEND_PORT=3000
API_BASE_URL=http://localhost:8080/api/v1
