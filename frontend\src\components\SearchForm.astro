---
// Modern search form with enhanced features
---

<div class="max-w-3xl mx-auto mb-12 search-container">
  <!-- Main search form -->
  <form id="search-form" class="relative mb-6">
    <div class="relative group">
      <!-- Search input with modern styling -->
      <input
        type="text"
        id="search-input"
        placeholder="Search anything..."
        class="w-full px-6 py-4 pr-16 text-lg border-2 border-gray-200 dark:border-gray-600 rounded-2xl focus:outline-none focus:ring-4 focus:ring-blue-500/20 focus:border-blue-500 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 transition-all duration-300 shadow-lg hover:shadow-xl group-hover:border-gray-300 dark:group-hover:border-gray-500"
        autocomplete="off"
        spellcheck="false"
      />

      <!-- Search button -->
      <button
        type="submit"
        class="absolute right-2 top-1/2 transform -translate-y-1/2 p-3 text-gray-500 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200 rounded-xl hover:bg-gray-100 dark:hover:bg-gray-700"
        aria-label="Search"
      >
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2.5" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
        </svg>
      </button>

      <!-- Clear button (hidden by default) -->
      <button
        type="button"
        id="clear-search"
        class="absolute right-14 top-1/2 transform -translate-y-1/2 p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors duration-200 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 hidden"
        aria-label="Clear search"
      >
        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
      </button>
    </div>

    <!-- Enhanced autocomplete with modern styling -->
    <div id="autocomplete" class="absolute top-full left-0 right-0 bg-white/95 dark:bg-gray-800/95 backdrop-blur-md border border-gray-200 dark:border-gray-600 rounded-2xl shadow-2xl mt-2 hidden z-50 overflow-hidden">
      <div class="p-2">
        <!-- Recent searches -->
        <div id="recent-searches" class="hidden">
          <div class="px-3 py-2 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wide">Recent Searches</div>
          <div id="recent-list"></div>
        </div>

        <!-- Suggestions -->
        <div id="suggestions-list"></div>

        <!-- Quick actions -->
        <div class="border-t border-gray-100 dark:border-gray-700 mt-2 pt-2">
          <div class="px-3 py-2 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wide">Quick Actions</div>
          <button type="button" class="w-full text-left px-3 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors duration-150 flex items-center gap-2">
            <svg class="w-4 h-4 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
            </svg>
            I'm Feeling Lucky
          </button>
        </div>
      </div>
    </div>
  </form>

  <!-- Search options and filters -->
  <div class="flex flex-wrap items-center justify-center gap-3 mb-6">
    <!-- Search type filters -->
    <div class="flex items-center gap-2">
      <span class="text-sm font-medium text-gray-600 dark:text-gray-400">Search:</span>
      <div class="flex rounded-xl bg-gray-100 dark:bg-gray-800 p-1">
        <button type="button" class="search-filter active px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200" data-filter="all">
          All
        </button>
        <button type="button" class="search-filter px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200" data-filter="web">
          Web
        </button>
        <button type="button" class="search-filter px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200" data-filter="images">
          Images
        </button>
        <button type="button" class="search-filter px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200" data-filter="news">
          News
        </button>
      </div>
    </div>

    <!-- Time filter -->
    <div class="flex items-center gap-2">
      <span class="text-sm font-medium text-gray-600 dark:text-gray-400">Time:</span>
      <select id="time-filter" class="px-3 py-2 text-sm border border-gray-200 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500">
        <option value="any">Any time</option>
        <option value="day">Past 24 hours</option>
        <option value="week">Past week</option>
        <option value="month">Past month</option>
        <option value="year">Past year</option>
      </select>
    </div>
  </div>

  <!-- Action buttons -->
  <div class="flex justify-center gap-4">
    <button type="submit" form="search-form" class="btn btn-primary px-8 py-3 text-base font-semibold shadow-lg hover:shadow-xl">
      <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
      </svg>
      Ultra Search
    </button>
    <button type="button" id="lucky-search" class="btn btn-secondary px-8 py-3 text-base font-semibold shadow-lg hover:shadow-xl">
      <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
      </svg>
      I'm Feeling Lucky
    </button>
  </div>
</div>

<style>
  .search-filter.active {
    @apply bg-blue-600 text-white shadow-md;
  }

  .search-filter:not(.active) {
    @apply text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100 hover:bg-gray-200 dark:hover:bg-gray-700;
  }
</style>
