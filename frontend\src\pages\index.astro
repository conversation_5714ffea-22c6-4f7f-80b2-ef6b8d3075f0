---
// Ultra Search - Streamlined Implementation
import Layout from '../layouts/Layout.astro';
import SearchHeader from '../components/SearchHeader.astro';
import SearchForm from '../components/SearchForm.astro';
import LoadingSpinner from '../components/LoadingSpinner.astro';
import SearchResults from '../components/SearchResults.astro';
import "../styles/global.css";
---

<Layout title="Ultra Search">
  <main class="min-h-screen flex flex-col">
    <!-- Main content area -->
    <div class="flex-1 container mx-auto px-4 py-8 max-w-6xl">
      <SearchHeader />
      <SearchForm />
      <LoadingSpinner />
      <SearchResults />
    </div>

    <!-- Footer -->
    <footer class="mt-auto py-8 border-t border-gray-200 dark:border-gray-700 bg-white/50 dark:bg-gray-900/50 backdrop-blur-sm">
      <div class="container mx-auto px-4 max-w-6xl">
        <div class="flex flex-col md:flex-row items-center justify-between gap-4">
          <div class="flex items-center gap-6 text-sm text-gray-600 dark:text-gray-400">
            <span>© 2024 Ultra Search</span>
            <a href="#" class="hover:text-blue-600 dark:hover:text-blue-400 transition-colors">Privacy</a>
            <a href="#" class="hover:text-blue-600 dark:hover:text-blue-400 transition-colors">Terms</a>
            <a href="#" class="hover:text-blue-600 dark:hover:text-blue-400 transition-colors">About</a>
          </div>

          <div class="flex items-center gap-4">
            <!-- Provider status indicators -->
            <div class="flex items-center gap-2 text-xs text-gray-500 dark:text-gray-400">
              <span>Powered by:</span>
              <div class="flex items-center gap-1">
                <div class="w-2 h-2 rounded-full bg-green-500" title="Google"></div>
                <div class="w-2 h-2 rounded-full bg-orange-500" title="Bing"></div>
                <div class="w-2 h-2 rounded-full bg-red-500" title="DuckDuckGo"></div>
                <div class="w-2 h-2 rounded-full bg-purple-500" title="Yahoo"></div>
                <div class="w-2 h-2 rounded-full bg-green-600" title="Startpage"></div>
                <div class="w-2 h-2 rounded-full bg-orange-600" title="Brave"></div>
              </div>
            </div>

            <!-- Social links -->
            <div class="flex items-center gap-2">
              <a href="#" class="p-2 text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors" title="GitHub">
                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
                </svg>
              </a>
              <a href="#" class="p-2 text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors" title="Twitter">
                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
                </svg>
              </a>
            </div>
          </div>
        </div>
      </div>
    </footer>
  </main>

  <script>
    import { SearchManager } from '../scripts/search.ts';
    new SearchManager();
  </script>
</Layout>
