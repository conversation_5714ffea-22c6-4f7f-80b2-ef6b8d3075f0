---
// Ultra Search - Streamlined Implementation
import Layout from '../layouts/Layout.astro';
import SearchHeader from '../components/SearchHeader.astro';
import SearchForm from '../components/SearchForm.astro';
import LoadingSpinner from '../components/LoadingSpinner.astro';
import SearchResults from '../components/SearchResults.astro';
import "../styles/global.css";
---

<Layout title="Ultra Search">
  <div class="container mx-auto px-4 py-16">
    <SearchHeader />
    <SearchForm />
    <LoadingSpinner />
    <SearchResults />
  </div>

  <script>
    import { SearchManager } from '../scripts/search.ts';
    new SearchManager();
  </script>
</Layout>
